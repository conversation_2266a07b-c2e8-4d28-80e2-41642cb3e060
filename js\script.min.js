//script.min.js

const labelMap = {
  "Such<PERSON>, Verarbeiten und Aufbewahren": "Such<PERSON>",
  "Kommunikation und Kollaborieren": "Kommunizieren",
  "Produzieren und Präsentieren": "Produzieren",
  "Schützen und sicher Agieren": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "Problemlösen und Handeln": "<PERSON>lösen",
  "Analysieren und Reflektieren": "Analysieren",
};

// Order for competence dimensions (left to right in chart as requested: suchen, komm, prod, schutz, problem, anal)
const categoryOrder = [
  "Suchen, Verarbeiten und Aufbewahren", // suchen
  "Kommunikation und Kollaborieren", // komm
  "Produzieren und Präsentieren", // prod
  "Schützen und sicher Agieren", // schutz
  "Problemlösen und Handeln", // problem
  "Analysieren und Reflektieren", // anal
];

// Function to sort categories in the specified order
function sortCategories(categories) {
  return [...categories].sort((a, b) => {
    return categoryOrder.indexOf(a) - categoryOrder.indexOf(b);
  });
}

const colorMap = {
  "Suchen, Verarbeiten und Aufbewahren": "#00BF63",
  "Kommunikation und Kollaborieren": "#0CC0DF",
  "Produzieren und Präsentieren": "#FF6D5F",
  "Schützen und sicher Agieren": "#8C52FF",
  "Problemlösen und Handeln": "#E884C4",
  "Analysieren und Reflektieren": "#FFD473",
  overall: "#999999", // ADDED: Color for "overall" category
};

const competencyDescriptions = {
  "Suchen, Verarbeiten und Aufbewahren":
    "Umfasst das Wissen, die Motivation und Fähigkeiten, gezielt nach digitalen Daten und Inhalten zu suchen, diese effektiv zu organisieren, zu speichern und abzurufen.",
  "Kommunikation und Kollaborieren":
    "Umfasst das Wissen, die Motivation und Fähigkeiten, mithilfe digitaler Technologien effektiv zu interagieren, zu kollaborieren und Informationen auszutauschen, dabei die Verhaltensnormen in digitalen Umgebungen zu beachten und digitale Technologien zur gesellschaftlichen Teilhabe und Selbstermächtigung zu nutzen.",
  "Produzieren und Präsentieren":
    "Umfasst das Wissen, die Motivation und Fähigkeiten, digitale Inhalte in verschiedenen Formaten zu erstellen, zu bearbeiten und zu integrieren, dabei Urheberrecht und Lizenzen zu berücksichtigen, sowie das Programmieren digitaler Produkte.",
  "Schützen und sicher Agieren":
    "Umfasst das Wissen, die Motivation und Fähigkeiten, digitale Geräte und Inhalte zu schützen, Gesundheits- und Umweltgefahren bei der Nutzung digitaler Technologien zu vermeiden, und persönliche Daten, Identität sowie Privatsphäre in digitalen Umgebungen verantwortungsvoll zu schützen.",
  "Problemlösen und Handeln":
    "Umfasst das Wissen, die Motivation und Fähigkeiten, technische Probleme zu erkennen und zu lösen und kreative technische Lösungen für spezifische Bedürfnisse zu finden. Zudem gehört zum Kompetenzbereich informatisches Denken, also das strategische Lösen komplexer Probleme in digitalen Umgebungen und die kontinuierliche Weiterentwicklung der eigenen digitalen Kompetenzen.",
  "Analysieren und Reflektieren":
    "Umfasst das Wissen, die Motivation und Fähigkeiten, die Auswirkungen und Verbreitung digitaler Medien und Inhalte zu analysieren, deren Glaubwürdigkeit und Zuverlässigkeit kritisch zu bewerten sowie Geschäftsaktivitäten in digitalen Umgebungen zu identifizieren und angemessen darauf zu reagieren.",
};

// Helper function to lighten a color
function lightenColor(hex, ratio = 0.4) {
  if (!hex.startsWith("#")) return hex; // Return if not a hex color
  let r = parseInt(hex.slice(1, 3), 16);
  let g = parseInt(hex.slice(3, 5), 16);
  let b = parseInt(hex.slice(5, 7), 16);
  r = Math.min(255, Math.floor(r + 255 * ratio));
  g = Math.min(255, Math.floor(g + 255 * ratio));
  b = Math.min(255, Math.floor(b + 255 * ratio));
  // Convert back to hex:
  return `#${r.toString(16).padStart(2, "0")}${g
    .toString(16)
    .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}

// Helper function to darken a color
function darkenColor(hex, ratio = 0.3) {
  if (!hex.startsWith("#")) return hex;
  let r = parseInt(hex.slice(1, 3), 16);
  let g = parseInt(hex.slice(3, 5), 16);
  let b = parseInt(hex.slice(5, 7), 16);
  r = Math.max(0, Math.floor(r - 255 * ratio));
  g = Math.max(0, Math.floor(g - 255 * ratio));
  b = Math.max(0, Math.floor(b - 255 * ratio));
  return `rgb(${r}, ${g}, ${b})`;
}

function downloadChart() {
  const canvas = document.getElementById("competencyChart1");
  if (!canvas) {
    console.error("Canvas element not found");
    Swal.fire("Fehler", "Diagramm nicht gefunden", "error");
    return;
  }

  try {
    // Get data from session storage
    const userCode =
      sessionStorage.getItem("generatedCode") ||
      sessionStorage.getItem("resultsCode") ||
      "Nutzer";
    const attemptNumber = sessionStorage.getItem("attemptNumber") || "1";
    const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const filename = `Kompetenzdiagramm_${userCode}_T${attemptNumber}_${timestamp}.png`;

    // Create a temporary link element to trigger the download
    const link = document.createElement("a");
    link.download = filename;
    link.href = canvas.toDataURL("image/png", 1.0);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log("Chart download initiated");
  } catch (error) {
    console.error("Error downloading chart:", error);
    Swal.fire(
      "Fehler",
      "Beim Herunterladen des Diagramms ist ein Fehler aufgetreten",
      "error"
    );
  }
}

//Helper function for description box
function updateDescriptionBox(box, category, description) {
  const borderColor = colorMap[category] || "#999999";
  const bgColor = lightenColor(borderColor);
  box.innerHTML = `
        <h3>${category}</h3>
        <p>${description || "Beschreibung nicht verfügbar."}</p>
    `;
  box.style.backgroundColor = bgColor;
  box.style.padding = "15px";
  box.style.borderRadius = "5px";
  box.style.border = `2px solid ${borderColor}`;
  box.style.color = getContrastColor(bgColor); // White or black text
}

// Helper function for contrast color
function getContrastColor(hex) {
  if (!hex.startsWith("#")) return "#000";
  const r = parseInt(hex.slice(1, 3), 16),
    g = parseInt(hex.slice(3, 5), 16),
    b = parseInt(hex.slice(5, 7), 16);
  // Calculate relative luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5 ? "#000000" : "#ffffff";
}

function createCompetencyChartConfig(
  categories,
  t1Data,
  t2Data,
  t3Data,
  canvasId,
  descriptionBoxId,
  userCode
) {
  const datasets = [];
  if (t1Data.some((value) => value > 0)) {
    datasets.push({
      label: "Erste Befragung",
      data: t1Data,
      backgroundColor: categories.map((c) =>
        lightenColor(colorMap[c] || "#999999", 0.4)
      ),
      borderColor: categories.map((c) => colorMap[c] || "#999999"),
      borderWidth: 1,
    });
  }
  if (t2Data.some((value) => value > 0)) {
    datasets.push({
      label: "Zweite Befragung",
      data: t2Data,
      backgroundColor: categories.map((c) => colorMap[c] || "#999999"),
      borderColor: categories.map((c) => colorMap[c] || "#999999"),
      borderWidth: 1,
    });
  }
  if (t3Data.some((value) => value > 0)) {
    datasets.push({
      label: "Dritte Befragung",
      data: t3Data,
      backgroundColor: categories.map((c) =>
        darkenColor(colorMap[c] || "#999999", 0.3)
      ),
      borderColor: categories.map((c) => colorMap[c] || "#999999"),
      borderWidth: 1,
    });
  }

  // Check if userCode should be displayed
  const shouldDisplayUserCode =
    userCode &&
    userCode !== "Unknown" &&
    userCode !== "undefined" &&
    userCode !== "Nutzer";

  const config = {
    type: "bar",
    data: {
      labels: categories.map((cat) => labelMap[cat] || cat),
      datasets: datasets,
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: "Score (%)",
          },
        },
        x: {
          ticks: {
            autoSkip: false,
            maxRotation: 45,
            minRotation: 45,
          },
        },
      },
      hover: {
        mode: "index",
        intersect: false,
      },
      plugins: {
        legend: {
          display: datasets.length > 1,
        },
        tooltip: {
          callbacks: {
            title: (tooltipItems) => {
              const index = tooltipItems[0].dataIndex;
              return categories[index] || tooltipItems[0].label;
            },
            label: (tooltipItem) =>
              `${tooltipItem.dataset.label}: ${tooltipItem.parsed.y}%`,
          },
        },
        annotation: {
          annotations: shouldDisplayUserCode
            ? [
                {
                  type: "label",
                  position: "top-right",
                  xValue: "end",
                  yValue: 100,
                  xAdjust: -10,
                  yAdjust: -10,
                  content: `User Code: ${userCode}`,
                  font: {
                    size: 12,
                    style: "italic",
                    color: "grey",
                  },
                },
              ]
            : [],
        },
        title: {
          display: shouldDisplayUserCode,
          position: "bottom",
          align: "end",
          text: `User Code: ${userCode}`,
          font: {
            size: 12,
            style: "italic",
            color: "grey",
          },
        },
      },
      onHover: (event, activeElements, chart) => {
        const descriptionBox = document.getElementById(descriptionBoxId);
        if (activeElements && activeElements.length > 0 && descriptionBox) {
          const index = activeElements[0].index;
          const cat = categories[index];
          updateDescriptionBox(
            descriptionBox,
            cat,
            competencyDescriptions[cat]
          );
        }
      },
    },
  };
  return config;
}

function setupCodeGenerationForm() {
  const form = document.getElementById("generateCodeForm");
  if (form) {
    form.addEventListener("submit", handleCodeGenerationFormSubmission);
  }
}
function handleOptionSelection(optionValue) {
  const codeInputField = document.getElementById("codeInput");
  const loginButton = document.getElementById("loginButton");
  const generateCodeButton = document.getElementById("generateCodeButton");
  const resultsSection = document.getElementById("resultsSection");

  if (optionValue === "register") {
    if (codeInputField) codeInputField.style.display = "none";
    if (loginButton) loginButton.style.display = "none";
    if (generateCodeButton) generateCodeButton.style.display = "block";
    if (resultsSection) resultsSection.style.display = "none";
  } else if (optionValue === "redo" || optionValue === "followup") {
    if (codeInputField) codeInputField.style.display = "block";
    if (loginButton) loginButton.style.display = "block";
    if (generateCodeButton) generateCodeButton.style.display = "none";
    if (resultsSection) resultsSection.style.display = "none";
  } else if (optionValue === "viewResults") {
    if (codeInputField) codeInputField.style.display = "none";
    if (loginButton) loginButton.style.display = "none";
    if (generateCodeButton) generateCodeButton.style.display = "none";
    if (resultsSection) resultsSection.style.display = "block";
  }
}

function setupLoginPageFunctionality() {
  const iliasInputs = document.querySelectorAll(
    'input[name="iliasCourseCompleted"]'
  );
  const secondQuestion = document.getElementById("secondQuestion");
  const choiceContainer = document.getElementById("choiceContainer");
  const surveyOptionInput = document.getElementById("surveyOption");
  const codeInputField = document.getElementById("codeInput");
  const loginButton = document.getElementById("loginButton");
  const generateCodeButton = document.getElementById("generateCodeButton");
  const resultsSection = document.getElementById("resultsSection");

  // Function to create choice cards
  function createChoiceCard(value, heading, iconClass) {
    const card = document.createElement("div");
    card.classList.add("choice-card");
    card.setAttribute("data-value", value);

    const icon = document.createElement("i");
    icon.className = `${iconClass} card-icon`;

    const h3 = document.createElement("h3");
    h3.textContent = heading;

    card.appendChild(icon);
    card.appendChild(h3);
    choiceContainer.appendChild(card);
  }

  // Check if all required elements exist
  if (
    iliasInputs.length &&
    secondQuestion &&
    choiceContainer &&
    surveyOptionInput
  ) {
    // Add event listeners to radio buttons
    iliasInputs.forEach((radio) => {
      radio.addEventListener("change", function () {
        console.log("Radio changed to:", this.value); // Add this for debugging

        // Clear previous cards
        choiceContainer.innerHTML = "";

        // Display the second question section
        secondQuestion.style.display = "block";

        // Reset other elements
        surveyOptionInput.value = "";
        if (codeInputField) codeInputField.style.display = "none";
        if (resultsSection) resultsSection.style.display = "none";
        if (loginButton) loginButton.style.display = "none";
        if (generateCodeButton) generateCodeButton.style.display = "none";

        // Create appropriate cards based on selection
        if (this.value === "no") {
          createChoiceCard(
            "register",
            "Ich möchte mich neu registrieren",
            "fas fa-user-plus"
          );
          createChoiceCard(
            "viewResults",
            "Möchten Sie Ihre bisherigen Ergebnisse noch einmal ansehen?",
            "fas fa-chart-bar"
          );
        } else if (this.value === "yes") {
          createChoiceCard(
            "redo",
            "Ich möchte den Fragebogen erneut ausfüllen",
            "fas fa-redo-alt"
          );
          createChoiceCard(
            "followup",
            "Follow-up Befragung (nach einem Monat)",
            "fas fa-clock"
          );
          createChoiceCard(
            "viewResults",
            "Möchten Sie Ihre bisherigen Ergebnisse noch einmal ansehen?",
            "fas fa-chart-bar"
          );
        }

        // Setup event handlers for the newly created cards
        const cards = document.querySelectorAll(".choice-card");
        cards.forEach((card) => {
          card.addEventListener("click", function () {
            cards.forEach((c) => c.classList.remove("selected"));
            this.classList.add("selected");
            const value = this.getAttribute("data-value");
            surveyOptionInput.value = value;
            handleOptionSelection(value);
          });
        });
      });
    });

    // Set up generate code button if it exists
    if (generateCodeButton) {
      generateCodeButton.addEventListener("click", function () {
        window.location.href = "generateCode.html";
      });
    }
  }
}

function setupNavigationButtons() {
  const buttonIds = ["letsGetStarted", "startSurvey"];
  buttonIds.forEach((btnId) => {
    const btn = document.getElementById(btnId);
    if (btn) {
      btn.addEventListener("click", function () {
        window.location.href = "login.html";
      });
    }
  });
}

async function handleCodeGenerationFormSubmission(e) {
  e.preventDefault();
  const form = e.target;
  if (!validateFormInputs(form)) return;

  sessionStorage.clear();
  const generatedCode = generateCodeFromForm(form);

  const submitBtn = form.querySelector('button[type="submit"]');
  submitBtn.disabled = true;
  submitBtn.textContent = "Registrierung läuft...";

  try {
    // Use FormData to send data
    const formData = new FormData();
    formData.append("code", generatedCode);

    const response = await fetch("php/register.php", {
      method: "POST",
      body: formData, // Send FormData
    });

    const responseData = await response.json();

    if (responseData.ok) {
      // Use userId as a number (important change!)
      sessionStorage.setItem("userId", responseData.userId);
      sessionStorage.setItem("generatedCode", generatedCode);

      await Swal.fire({
        icon: "success",
        title: "Erfolg",
        text: "Dein Code wurde erfolgreich registriert!",
        timer: 2000,
        showConfirmButton: false,
      });
      window.location.href = "codeConfirmation.html";
    } else if (responseData.isDuplicateCode) {
      await Swal.fire({
        icon: "error",
        title: "Code bereits vergeben",
        text: "Dieser Code existiert bereits. Bitte verwenden Sie stattdessen die Initialen Ihrer Erziehungsberechtigten für den zweiten Teil des Codes.",
      });
      updateParentFieldForFather();
    } else {
      await Swal.fire({
        icon: "error",
        title: "Registrierungsfehler",
        text: `Fehler beim Registrieren des Codes: ${
          responseData.message || "Unbekannter Fehler"
        }`,
      });
    }
  } catch (err) {
    console.error("Error registering code:", err);
    await Swal.fire({
      icon: "error",
      title: "Registrierungsfehler",
      text: "Es gab einen Fehler bei der Registrierung. Bitte versuchen Sie es später erneut.",
    });
  } finally {
    submitBtn.disabled = false;
    submitBtn.textContent = "Code Generieren";
  }
}

function updateParentFieldForFather() {
  const label = document.getElementById("parentLabel");
  const instructions = document.getElementById("parentInstructions");
  const inputField = document.getElementById("parentName");
  if (label && instructions && inputField) {
    label.textContent = "Vorname des Vaters / Ihres Erziehungsberechtigten";
    instructions.textContent =
      "Bitte geben Sie den ersten und letzten Buchstaben des Vornamens Ihres Vaters ein. Bsp.: Thomas = TS";
    inputField.value = "";
    inputField.focus();
  }
}

function validateFormInputs(form) {
  const textInputs = form.querySelectorAll('input[type="text"]');
  let isValid = true;

  textInputs.forEach((input) => {
    const value = input.value.trim();
    // Get label text robustly, handling potential structure changes
    const labelElement = input.labels
      ? input.labels[0]
      : input.previousElementSibling;
    const labelText = labelElement ? labelElement.textContent.trim() : input.id; // Fallback to id

    // Reset previous custom validity
    input.setCustomValidity("");

    if (value.length !== 2) {
      const message = `Bitte geben Sie genau zwei Zeichen für ${labelText} ein.`;
      input.setCustomValidity(message); // Set custom validity for browser reporting
      Swal.fire({ icon: "error", title: "Ungültige Eingabe", text: message });
      isValid = false;
      return; // Stop checking this input
    }

    switch (input.id) {
      case "birthplace":
      case "parentName":
      case "school":
        // *** CHANGE THE REGEX HERE ***
        // Allow A-Z, a-z, German Umlauts, and ß (exactly 2 characters)
        if (!/^[A-Za-zäöüÄÖÜß]{2}$/.test(value)) {
          const message = `${labelText} muss aus zwei Buchstaben, Umlauten oder ß bestehen.`;
          input.setCustomValidity(message);
          Swal.fire({
            icon: "error",
            title: "Ungültige Eingabe",
            text: message,
          });
          isValid = false;
        }
        break;
      case "birthday":
        const dayNumber = parseInt(value, 10);
        if (!/^\d{2}$/.test(value) || dayNumber < 1 || dayNumber > 31) {
          const message = `${labelText} muss eine gültige Zahl zwischen 01 und 31 sein.`;
          input.setCustomValidity(message);
          Swal.fire({
            icon: "error",
            title: "Ungültige Eingabe",
            text: message,
          });
          isValid = false;
        }
        break;
      default:
        break;
    }
    // If an error occurred for this input, stop further checks for this input
    if (!isValid) return;
  });

  // Optional: If you want the browser's default validation UI to trigger on submit
  // if (!isValid) {
  //     form.reportValidity();
  // }

  return isValid;
}

function generateCodeFromForm(form) {
  return (
    document.getElementById("birthplace").value.toUpperCase() +
    "-" +
    document.getElementById("parentName").value.toUpperCase() +
    "-" +
    document.getElementById("birthday").value +
    "-" +
    document.getElementById("school").value.toUpperCase()
  );
}

// Removed: submitForm function (no longer needed)

function handleLoginFormSubmission() {
  const loginForm = document.getElementById("loginForm");
  if (loginForm) {
    loginForm.addEventListener("submit", function (event) {
      event.preventDefault();
      handleLogin();
    });
  }
}

async function handleLogin() {
  const surveyOption = document.getElementById("surveyOption")?.value;
  const loginCodeValue = (
    document.getElementById("loginCode")?.value || ""
  ).trim();

  console.log("Login attempt with:", { surveyOption, code: loginCodeValue });

  if (!surveyOption) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte wählen Sie eine Option aus.",
    });
    return;
  }

  if (surveyOption === "register") {
    window.location.href = "generateCode.html";
    return;
  }

  if (!loginCodeValue) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte geben Sie Ihren persönlichen Code ein.",
    });
    return;
  }

  const loginBtn = document.getElementById("loginButton");
  const generateBtn = document.getElementById("generateCodeButton");
  if (loginBtn) {
    loginBtn.disabled = true;
    loginBtn.textContent = "Login läuft...";
  }
  if (generateBtn) {
    generateBtn.disabled = true;
  }

  try {
    console.log("Preparing login request...");
    const formData = new FormData();
    formData.append("code", loginCodeValue);
    formData.append("surveyOption", surveyOption);

    if (surveyOption === "redo") {
      formData.append("startNewAttempt", "true");
      formData.append("attemptType", "T2");
    } else if (surveyOption === "followup") {
      formData.append("startNewAttempt", "true");
      formData.append("attemptType", "T3");
    }

    console.log("Sending login request...");
    const response = await fetch("php/login.php", {
      method: "POST",
      body: formData,
    });

    console.log("Response status:", response.status);
    const responseText = await response.text();
    console.log("Raw response:", responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      console.error("Failed to parse JSON response:", e);
      throw new Error("Server returned invalid JSON. Check the server logs.");
    }

    if (!response.ok) {
      throw new Error(
        responseData.message || `HTTP error! status: ${response.status}`
      );
    }

    console.log("Login successful:", responseData);

    sessionStorage.setItem("userId", responseData.userId);
    sessionStorage.setItem("attemptNumber", responseData.attemptNumber);

    // Set startTimestamp for the new attempt if it's T2 or T3
    const attemptNumber = parseInt(responseData.attemptNumber, 10);
    const startTimestampKey = `t${attemptNumber}_startTimestamp`;

    if (attemptNumber === 2 || attemptNumber === 3) {
      // For T2 and T3, always set a new start timestamp upon login for that attempt
      sessionStorage.setItem(startTimestampKey, new Date().toISOString());
      console.log(
        `Start timestamp for attempt ${attemptNumber} set upon login.`
      );
    }

    if (surveyOption === "redo" || surveyOption === "followup") {
      sessionStorage.setItem("currentSection", "0"); // Start at personal info for T2/T3
    }

    // Fetch tXComplete status if available from login.php (optional, depends on login.php)
    sessionStorage.setItem(
      "t1Complete",
      responseData.t1Complete ? "true" : "false"
    );
    sessionStorage.setItem(
      "t2Complete",
      responseData.t2Complete ? "true" : "false"
    );
    sessionStorage.setItem(
      "t3Complete",
      responseData.t3Complete ? "true" : "false"
    );

    console.log("Session storage set, redirecting to survey.html...");
    await new Promise((resolve) => setTimeout(resolve, 500));
    window.location.href = "survey.html";
  } catch (err) {
    console.error("Error during login:", err);
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: `Beim Login ist ein Fehler aufgetreten: ${err.message}`,
    });
  } finally {
    if (loginBtn) {
      loginBtn.disabled = false;
      loginBtn.textContent = "Weiter";
    }
    if (generateBtn) {
      generateBtn.disabled = false;
    }
  }
}

function setupLogoutFunctionality() {
  const logoutBtn = document.getElementById("logoutButton");
  if (logoutBtn) {
    logoutBtn.addEventListener("click", function () {
      sessionStorage.clear();
      window.location.href = "login.html";
    });
  }
}

// Removed: isNewUser function (no longer needed)

// Removed: initializeFlatpickr function (no longer needed)

function displayGeneratedCode() {
  const codeTextEl = document.getElementById("codeText");
  const generatedCode = sessionStorage.getItem("generatedCode");
  if (codeTextEl && generatedCode) {
    codeTextEl.textContent = generatedCode;
  } else if (codeTextEl) {
    codeTextEl.textContent = "Kein Code verfügbar oder Sitzung abgelaufen.";
  }
}

async function setupStartSurveyButton() {
  const startSurveyButton = document.getElementById("startSurveyButton");
  if (startSurveyButton) {
    startSurveyButton.addEventListener("click", async function () {
      try {
        const generatedCode = sessionStorage.getItem("generatedCode");

        if (!generatedCode) {
          Swal.fire({
            icon: "error",
            title: "Fehler",
            text: "Code nicht gefunden. Bitte erneut registrieren.",
          });
          window.location.href = "generateCode.html";
          return;
        }

        sessionStorage.setItem("attemptNumber", "1"); // T1

        const formData = new FormData();
        formData.append("code", generatedCode);
        formData.append("surveyOption", "register");

        const response = await fetch("php/login.php", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Login Fehler");
        }

        const responseData = await response.json();
        sessionStorage.setItem("userId", responseData.userId);
        sessionStorage.setItem("attemptNumber", responseData.attemptNumber); // Should be 1

        // Set T1 start timestamp here, as this is the true start of T1 survey
        if (parseInt(responseData.attemptNumber, 10) === 1) {
          sessionStorage.setItem("t1_startTimestamp", new Date().toISOString());
          console.log("T1 start timestamp set after registration and login.");
        }

        sessionStorage.setItem(
          "t1Complete",
          responseData.t1Complete ? "true" : "false"
        );
        sessionStorage.setItem(
          "t2Complete",
          responseData.t2Complete ? "true" : "false"
        );
        sessionStorage.setItem(
          "t3Complete",
          responseData.t3Complete ? "true" : "false"
        );

        window.location.href = "survey.html";
      } catch (err) {
        console.error("Error initializing survey:", err);
        Swal.fire({
          icon: "error",
          title: "Fehler",
          text: "Beim Starten der Befragung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.",
        });
      }
    });
  }
}

function initializeCopyCodeFunctionality() {
  const copyButton = document.getElementById("copyCodeButton");
  const codeTextEl = document.getElementById("codeText");
  if (copyButton && codeTextEl) {
    copyButton.addEventListener("click", function (e) {
      e.stopPropagation();
      const codeToCopy = codeTextEl.textContent.trim();
      navigator.clipboard
        .writeText(codeToCopy)
        .then(() => {
          Swal.fire({
            icon: "success",
            title: "Code kopiert!",
            text: "Dein Code wurde in die Zwischenablage kopiert.",
            timer: 1500,
            showConfirmButton: false,
          });
        })
        .catch((err) => {
          console.error("Failed to copy code: ", err);
          Swal.fire({
            icon: "error",
            title: "Kopieren fehlgeschlagen",
            text: "Es gab ein Problem beim Kopieren des Codes.",
          });
        });
    });
  }
}

async function setupViewResultsButton() {
  const viewResultsBtn = document.getElementById("viewResultsButton");
  const resultsCodeInput = document.getElementById("resultsCode");

  if (viewResultsBtn && resultsCodeInput) {
    viewResultsBtn.addEventListener("click", async () => {
      const codeVal = resultsCodeInput.value.trim();
      if (!codeVal) {
        Swal.fire({
          icon: "error",
          title: "Fehler",
          text: "Bitte geben Sie Ihren persönlichen Code ein, um Ergebnisse anzusehen.",
        });
        return;
      }

      try {
        const checkResp = await fetch("php/check-results.php", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ code: codeVal }),
        });

        let checkData;
        try {
          checkData = await checkResp.json();
        } catch (e) {
          console.error("Response parsing error:", e);
          throw new Error("Serverfehler: Ungültige Antwort");
        }

        if (!checkData.success) {
          Swal.fire({
            icon: "error",
            title: "Ergebnisse nicht verfügbar",
            text: checkData.message || "Ein unbekannter Fehler ist aufgetreten",
          });
          return;
        }

        // Clear previous session data, set the userId, and redirect
        sessionStorage.clear();
        sessionStorage.setItem("userId", checkData.userId);
        sessionStorage.setItem("resultsCode", codeVal);
        window.location.href = "results.html";
      } catch (err) {
        console.error("Error checking results:", err);
        Swal.fire({
          icon: "error",
          title: "Serverfehler",
          text: err.message || "Bitte versuchen Sie es später erneut.",
        });
      }
    });
  }
}

// Function to format code input (uppercase and hyphens)
function formatCodeInput(input) {
  // 1. Get the current value
  let value = input.value;

  // 2. Remove any characters that are NOT letters (A-Z, a-z, umlauts, ß) or digits (0-9)
  //    This will also remove hyphens typed by the user, which is fine as we add them back.
  let cleanedValue = value.replace(/[^A-Za-z0-9äöüÄÖÜß]/g, "");

  // 3. Convert the cleaned value to uppercase (using PHP's mb_strtoupper equivalent is tricky in JS,
  //    but standard toUpperCase handles most common cases well enough for display formatting,
  //    the backend mb_strtoupper handles the final correct conversion).
  //    Alternatively, for pure JS uppercase including umlauts:
  //    let upperValue = cleanedValue.toLocaleUpperCase('de-DE'); // Use German locale
  let upperValue = cleanedValue.toUpperCase(); // Standard JS uppercase often works visually

  // 4. Limit length to 8 characters (2+2+2+2)
  upperValue = upperValue.substring(0, 8);

  // 5. Re-add hyphens based on the length of the uppercased, cleaned value
  let formatted = "";
  if (upperValue.length > 0) {
    formatted += upperValue.substring(0, 2);
  }
  if (upperValue.length > 2) {
    formatted += "-" + upperValue.substring(2, 4);
  }
  if (upperValue.length > 4) {
    formatted += "-" + upperValue.substring(4, 6);
  }
  if (upperValue.length > 6) {
    formatted += "-" + upperValue.substring(6, 8);
  }

  // 6. Update the input field's value
  input.value = formatted;
}

function setupCodeInputFormatting() {
  const codeInputs = document.querySelectorAll("#loginCode, #resultsCode");
  codeInputs.forEach((input) => {
    input.addEventListener("input", () => formatCodeInput(input));
  });
}

// Hide redundant user code display in charts and charts' legends (and other elements) dynamically added to the DOM
function hideChartUserCode() {
  // First attempt to hide immediately
  const hideElements = () => {
    const chartFooters = document.querySelectorAll(
      ".chart-user-code, .chartjs-copyright, canvas + div"
    );
    chartFooters.forEach((el) => {
      if (el && el.textContent && el.textContent.includes("User Code:")) {
        el.style.display = "none";
      }
    });
  };

  // Initial hide attempt
  hideElements();

  // Set up a MutationObserver to watch for dynamically added elements
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        hideElements(); // Try to hide elements again when DOM changes
      }
    });
  });

  // Start observing with a more specific configuration
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true,
    attributes: true, // Watch for attribute changes
  });

  // Additional timeout to catch late-rendered elements
  setTimeout(hideElements, 1000);
  setTimeout(hideElements, 2000);
}

/* Main DOMContentLoaded */
document.addEventListener("DOMContentLoaded", function () {
  setupNavigationButtons();
  setupCodeGenerationForm();
  handleLoginFormSubmission();
  displayGeneratedCode();
  setupLogoutFunctionality();
  setupLoginPageFunctionality();
  setupStartSurveyButton();
  initializeCopyCodeFunctionality();
  setupViewResultsButton();
  setupCodeInputFormatting();
  setTimeout(hideChartUserCode, 500);
  // Removed: Flatpickr initialization (no longer used)
});

window.downloadChart = downloadChart;
window.createCompetencyChartConfig = createCompetencyChartConfig;
window.updateDescriptionBox = updateDescriptionBox;
window.handleOptionSelection = handleOptionSelection;
window.sortCategories = sortCategories;
