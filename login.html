<!-- login.html -->
<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Include Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <title>Login - Open Digi</title>
    <link rel="stylesheet" href="css/login.css" />
    <!-- Font Awesome for Icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- <PERSON><PERSON>lert2 CSS for better alerts -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css"
    />
    <!-- Optional: Include SweetAlert2 JS for better alerts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  </head>
  <body>
    <div class="login-container">
      <a href="index.html">
        <img src="images/logo.png" alt="Open Digi Logo" class="logo" />
      </a>

      <form id="loginForm" action="php/login.php" method="post">
        <!-- First Question -->
        <fieldset class="form-group">
          <legend>
            Haben Sie bereits an der ersten Befragung teilgenommen?
          </legend>
          <div class="radio-container">
            <label>
              <input type="radio" name="iliasCourseCompleted" value="no" /> Nein
            </label>
            <label>
              <input type="radio" name="iliasCourseCompleted" value="yes" /> Ja
            </label>
          </div>
        </fieldset>

        <!-- Second Question (Options will be shown based on the first answer) -->
        <div id="secondQuestion" style="display: none">
          <fieldset class="form-group">
            <legend>Wie möchten Sie fortfahren?</legend>
            <div class="choice-container" id="choiceContainer">
              <!-- Choice cards will be generated dynamically -->
            </div>
          </fieldset>
        </div>

        <!-- Hidden input to store the selected option -->
        <input type="hidden" id="surveyOption" name="surveyOption" required />

        <!-- Code Input -->
        <div id="codeInput" style="display: none">
          <div class="input-group">
            <label for="loginCode">
              Geben Sie hier Ihren persönlichen Code ein. Bitte geben Sie den
              Code mit Bindestrich ein, z.B. GW-SI-06-KE
            </label>
            <input
              type="text"
              id="loginCode"
              name="loginCode"
              required
              placeholder="GW-SI-06-KE"
            />
          </div>
        </div>

        <!-- Buttons -->
        <button
          type="submit"
          id="loginButton"
          class="primary-button"
          style="display: none"
        >
          Weiter
        </button>
      </form>

      <!-- Generate Code Button -->
      <button
        type="button"
        id="generateCodeButton"
        class="primary-button"
        style="display: none"
      >
        Klicken Sie hier, um einen neuen Code zu generieren
      </button>

      <div id="resultsSection" style="display: none">
        <div class="input-group">
          <label for="resultsCode">
            Geben Sie hier bitte Ihren Code ein, um Ihre bisherigen Ergebnisse
            anzeigen zu lassen.
          </label>
          <input
            type="text"
            id="resultsCode"
            name="resultsCode"
            placeholder="GW-SI-06-KE"
            autocomplete="off"
          />
        </div>
        <button type="button" id="viewResultsButton" class="primary-button">
          Ergebnisse anzeigen
        </button>
      </div>
    </div>
    <script src="js/survey-data.min.js"></script>
    <script src="js/script.min.js"></script>
  </body>
</html>
