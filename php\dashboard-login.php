<?php
declare(strict_types=1);
header('Content-Type: application/json');
session_start(); // Start the session

// Basic session fixation prevention
if (!isset($_SESSION['initiated'])) {
    session_regenerate_id();
    $_SESSION['initiated'] = true;
}

require_once __DIR__ . '/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$userIdInput = trim($data['userId'] ?? ''); // Use a different variable name
$password = trim($data['password'] ?? '');

if ($userIdInput === '' || $password === '') {
    http_response_code(400);
    echo json_encode(['error' => 'Missing credentials']);
    exit;
}

// Fetch the DB user ID (numeric) and the dashboard username (text) along with the hash
$sql = 'SELECT id, user_id, password FROM dashboard_users WHERE user_id = ? LIMIT 1';
$stmt = $conn->prepare($sql);
if (!$stmt) {
    error_log("Prepare failed: (" . $conn->errno . ") " . $conn->error);
    http_response_code(500);
    echo json_encode(['error' => 'Database error (prepare)']);
    exit;
}

$stmt->bind_param('s', $userIdInput);
$stmt->execute();
$stmt->bind_result($uid, $db_user_id, $storedHash); // Bind both IDs

if ($stmt->fetch() && password_verify($password, $storedHash)) {
    // Regenerate session ID *after* successful login to prevent fixation
    session_regenerate_id(true);

    // Store login state and user info in the session
    $_SESSION['logged_in'] = true;
    $_SESSION['dashboard_user_db_id'] = $uid;          // Store the numeric DB ID
    $_SESSION['dashboard_username'] = $db_user_id;   // Store the dashboard username (e.g., 'opendigi24')

    echo json_encode([
        'ok' => true,
        'message' => 'Login successful'
        // No need to return a token if using sessions for page/data protection
    ]);

} else {
    // Optional: Implement rate limiting or clear session attempts here
    http_response_code(401);
    echo json_encode(['error' => 'Invalid credentials']);
}

$stmt->close();
$conn->close();
?>