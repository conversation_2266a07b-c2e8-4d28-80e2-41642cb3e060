<?php
require_once('db_connect.php');
ini_set('display_errors', 0); // Turn off HTML error display for production
ini_set('log_errors', 1);    // Log errors to server log
// error_log("get_user_data.php called");

header('Content-Type: application/json');

// Initialize statement variables to null
$user_stmt = null;
$responses_stmt = null;
$pre_survey_stmt = null;
$open_ended_stmt = null;
$scores_stmt = null;

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = isset($_GET['userId']) ? intval($_GET['userId']) : 0;
        // error_log("get_user_data.php - User ID: " . $userId);

        if (!$userId) {
            throw new Exception('Invalid or missing userId parameter');
        }

        // Fetch user data
        $user_query = "SELECT id, user_code, attempt_number, is_complete, current_section, datenschutz_consent, unterschrift,
                              t1_startTimestamp, t1_endTimestamp,
                              t2_startTimestamp, t2_endTimestamp,
                              t3_startTimestamp, t3_endTimestamp
                       FROM users WHERE id = ?";
        $user_stmt = $conn->prepare($user_query);
        if (!$user_stmt) { throw new Exception("Prepare failed (user_query): " . $conn->error); }
        $user_stmt->bind_param("i", $userId);
        if (!$user_stmt->execute()) { throw new Exception("Execute failed (user_query): " . $user_stmt->error); }
        $user_result = $user_stmt->get_result();

        if ($user_result->num_rows === 1) {
            $user = $user_result->fetch_assoc();
            // error_log("get_user_data.php - User data fetched: " . json_encode($user));

            // Fetch responses
            $responses_query = "SELECT question_id, response, attempt FROM responses WHERE user_id = ?";
            $responses_stmt = $conn->prepare($responses_query);
            if (!$responses_stmt) { throw new Exception("Prepare failed (responses_query): " . $conn->error); }
            $responses_stmt->bind_param("i", $userId);
            if (!$responses_stmt->execute()) { throw new Exception("Execute failed (responses_query): " . $responses_stmt->error); }
            $responses_result = $responses_stmt->get_result();
            $initialResponses = [];
            $updatedResponses = [];
            $followUpResponses = [];
            while ($row = $responses_result->fetch_assoc()) {
                if ($row['attempt'] == 1) $initialResponses[$row['question_id']] = $row['response'];
                else if ($row['attempt'] == 2) $updatedResponses[$row['question_id']] = $row['response'];
                else if ($row['attempt'] == 3) $followUpResponses[$row['question_id']] = $row['response'];
            }
            // DO NOT CLOSE $responses_stmt here

            // Fetch pre-survey responses
            $pre_survey_query = "SELECT question_id, response FROM pre_survey_responses WHERE user_id = ?";
            $pre_survey_stmt = $conn->prepare($pre_survey_query);
            if (!$pre_survey_stmt) { throw new Exception("Prepare failed (pre_survey_query): " . $conn->error); }
            $pre_survey_stmt->bind_param("i", $userId);
            if (!$pre_survey_stmt->execute()) { throw new Exception("Execute failed (pre_survey_query): " . $pre_survey_stmt->error); }
            $pre_survey_result = $pre_survey_stmt->get_result();
            $pre_survey_responses = [];
            while ($row = $pre_survey_result->fetch_assoc()) {
                $pre_survey_responses[$row['question_id']] = $row['response'];
            }
            // DO NOT CLOSE $pre_survey_stmt here

            // Fetch open-ended responses
            $open_ended_query = "SELECT question_key, response, attempt FROM open_ended_responses WHERE user_id = ?";
            $open_ended_stmt = $conn->prepare($open_ended_query);
            if (!$open_ended_stmt) { throw new Exception("Prepare failed (open_ended_query): " . $conn->error); }
            $open_ended_stmt->bind_param("i", $userId);
            if (!$open_ended_stmt->execute()) { throw new Exception("Execute failed (open_ended_query): " . $open_ended_stmt->error); }
            $open_ended_result = $open_ended_stmt->get_result();
            $openEndedResponses = [];
            while ($row = $open_ended_result->fetch_assoc()) {
                $openEndedResponses[$row['question_key']] = $row['response'];
            }
            // DO NOT CLOSE $open_ended_stmt here

            // Fetch scores
            $scores_query = "SELECT attempt_number, category, score FROM scores WHERE user_id = ?";
            $scores_stmt = $conn->prepare($scores_query);
            if (!$scores_stmt) { throw new Exception("Prepare failed (scores_query): " . $conn->error); }
            $scores_stmt->bind_param("i", $userId);
            if (!$scores_stmt->execute()) { throw new Exception("Execute failed (scores_query): " . $scores_stmt->error); }
            $scores_result = $scores_stmt->get_result();
            $initialScores = [];
            $updatedScores = [];
            $followUpScores = [];
            while ($row = $scores_result->fetch_assoc()) {
                if ($row['attempt_number'] == 1) $initialScores[$row['category']] = $row['score'];
                else if ($row['attempt_number'] == 2) $updatedScores[$row['category']] = $row['score'];
                else if ($row['attempt_number'] == 3) $followUpScores[$row['category']] = $row['score'];
            }
            // DO NOT CLOSE $scores_stmt here

            $userData = [
                'userId' => $user['id'],
                'userCode' => $user['user_code'],
                'attemptNumber' => $user['attempt_number'] ?? 1,
                'isComplete' => (bool)($user['is_complete'] ?? 0),
                'currentSection' => $user['current_section'] ?? -2,
                'datenschutzConsent' => (bool)($user['datenschutz_consent'] ?? 0),
                'unterschrift' => $user['unterschrift'] ?? '',
                'timeStamps' => [
                    't1' => ['start' => $user['t1_startTimestamp'], 'end' => $user['t1_endTimestamp']],
                    't2' => ['start' => $user['t2_startTimestamp'], 'end' => $user['t2_endTimestamp']],
                    't3' => ['start' => $user['t3_startTimestamp'], 'end' => $user['t3_endTimestamp']],
                ],
                'initialResponses' => $initialResponses,
                'updatedResponses' => $updatedResponses,
                'followUpResponses' => $followUpResponses,
                'preSurveyResponses' => $pre_survey_responses,
                'openEndedResponses' => $openEndedResponses,
                'initialScores' => $initialScores,
                'updatedScores' => $updatedScores,
                'followUpScores' => $followUpScores
            ];
            echo json_encode($userData);
        } else {
            throw new Exception("User not found with ID: $userId");
        }
    } else {
        throw new Exception("Invalid request method");
    }
} catch (Exception $e) {
    error_log("Error in get_user_data.php: " . $e->getMessage() . "\nStack trace:\n" . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(["ok" => false, "message" => "Server error: " . $e->getMessage()]);
} finally {
    // Close statements if they were successfully prepared
    if ($user_stmt instanceof mysqli_stmt) $user_stmt->close();
    if ($responses_stmt instanceof mysqli_stmt) $responses_stmt->close();
    if ($pre_survey_stmt instanceof mysqli_stmt) $pre_survey_stmt->close();
    if ($open_ended_stmt instanceof mysqli_stmt) $open_ended_stmt->close();
    if ($scores_stmt instanceof mysqli_stmt) $scores_stmt->close();
    if (isset($conn) && $conn instanceof mysqli) $conn->close();
}
?>