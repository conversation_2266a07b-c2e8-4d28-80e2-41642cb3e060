
<?php
session_start(); // MUST be the very first line
// Check if the user is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    // Not logged in, redirect to the login page
    header('Location: dashboard-login.html');
    exit; // Crucial to stop script execution after redirection
}
?>


<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Survey Dashboard</title>

    <!-- Bootstrap and FontAwesome -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <!-- Flatpickr -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Your custom dashboard CSS -->
    <link rel="stylesheet" href="css/dashboard.css" />
    <!-- Chart.js for visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>
  <body class="container-fluid">
    <div class="text-end p-2 border-bottom mb-3">
        <span>Welcome, <?php echo htmlspecialchars($_SESSION['dashboard_username'] ?? 'User'); ?>!</span>
        <a href="php/dashboard-logout.php" class="btn btn-sm btn-outline-danger ms-2">Logout</a>
    </div>
    <h1 class="my-4 text-center dashboard-title">Survey Dashboard</h1>
    <div id="loadingIndicator" style="display: none" class="alert alert-info">
      <i class="fas fa-spinner fa-spin me-2"></i> Loading...
    </div>
    <div
      class="alert alert-danger"
      id="errorMessage"
      role="alert"
      style="display: none"
    ></div>

    <!-- Top control panel -->
    <section class="mb-3 controls-panel">
      <div class="d-flex flex-wrap gap-2">
        <button id="exportSelected" class="btn btn-primary btn-sm">
          <i class="fas fa-file-export me-1"></i>Export Selected
        </button>
        <button id="exportAll" class="btn btn-secondary btn-sm">
          <i class="fas fa-download me-1"></i>Export All
        </button>
        <!-- Toggle All Visible button will be added here by JS -->
        <button id="toggleVisualization" class="btn btn-info btn-sm ms-auto">
          <i class="fas fa-chart-line me-1"></i>Show Visualization
        </button>
      </div>
    </section>

    <!-- Search & Date Filter row -->
    <section class="mb-3 filter-panel">
      <div class="row gx-2 gy-2"> <!-- Added gy-2 for vertical gap on small screens -->
        <div class="col-lg-5 col-md-6"> <!-- Adjusted column sizing -->
          <div class="input-group input-group-sm">
            <input
              type="text"
              id="userSearch"
              class="form-control"
              placeholder="Search users (real-time)..."
            />
            <button
              class="btn btn-outline-secondary"
              type="button"
              id="searchButton"
            >
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="col-lg-7 col-md-6">
          <div class="input-group input-group-sm">
            <span class="input-group-text">Date Range</span>
            <input
              type="text"
              id="dateRange"
              class="form-control date-range-picker"
              placeholder="Select date range"
            />
            <!-- Clear Filters button will be added here by JS -->
          </div>
        </div>
      </div>
    </section>

    <!-- The wide table with T1, T2, T3 columns for all questions -->
    <div class="table-responsive">
      <table id="userTable" class="table table-bordered">
        <thead>
          <tr>
            <!-- We'll fill HEAD dynamically in dashboard.js -->
          </tr>
        </thead>
        <tbody>
          <!-- We'll fill ROWS dynamically in dashboard.js -->
        </tbody>
      </table>
    </div>

    <!-- Pagination area -->
    <nav aria-label="Pagination" class="mt-3">
      <div
        id="pagination"
        class="d-flex justify-content-between align-items-center"
      ></div>
    </nav>

    <!-- Visualization overlay (if needed) -->
    <div id="visualizationOverlay" class="overlay"></div>
    <aside id="visualizationSidebar" class="visualization-sidebar">
      <div class="visualization-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          Data Visualization for User: <span id="visualizationUserCode"></span>
        </h5>
        <button
          type="button"
          class="btn-close"
          aria-label="Close visualization"
          id="closeVisualization"
        ></button>
      </div>
      <div class="visualization-content p-2">
        <canvas id="visualization"></canvas>
        <button id="downloadVisualization" class="btn btn-secondary mt-3 w-100">
          <i class="fas fa-download me-2"></i>Download Visualization
        </button>
      </div>
    </aside>

    <!-- Flatpickr -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Include your minified script.js -->
    <script src="js/script.min.js"></script>
    <script src="js/survey-data.min.js"></script>
    <!-- Your main dashboard logic -->
    <script src="js/dashboard.js"></script>
  </body>
</html>
