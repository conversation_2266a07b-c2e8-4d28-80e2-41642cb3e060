<?php
// Error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

require_once __DIR__ . '/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $userId = isset($_POST['userId']) ? (int)$_POST['userId'] : 0;
        $attemptNumber = isset($_POST['attemptNumber']) ? (int)$_POST['attemptNumber'] : 1;
        $isComplete = isset($_POST['isComplete']) && $_POST['isComplete'] === 'true';
        $currentSection = isset($_POST['currentSection']) ? (int)$_POST['currentSection'] : 0;
        $datenschutzConsent = isset($_POST['datenschutzConsent']) && $_POST['datenschutzConsent'] === 'true';
        $unterschrift = isset($_POST['unterschrift']) ? $_POST['unterschrift'] : '';

        $startTimestampKey = "t{$attemptNumber}_startTimestamp";
        $endTimestampKey = "t{$attemptNumber}_endTimestamp";

        $postedStartTimestampISO = isset($_POST[$startTimestampKey]) ? $_POST[$startTimestampKey] : null;
        $postedEndTimestampISO = ($isComplete && isset($_POST[$endTimestampKey])) ? $_POST[$endTimestampKey] : null;

        function format_iso_to_mysql_datetime($isoString) {
            if ($isoString === null) {
                return null;
            }
            try {
                $dt = new DateTime($isoString);
                return $dt->format('Y-m-d H:i:s');
            } catch (Exception $e) {
                error_log("Invalid timestamp format for conversion: " . $isoString . " - Error: " . $e->getMessage());
                return null;
            }
        }

        $postedStartTimestamp = format_iso_to_mysql_datetime($postedStartTimestampISO);
        $postedEndTimestamp = format_iso_to_mysql_datetime($postedEndTimestampISO);

        if ($userId <= 0) {
            throw new Exception("Invalid userId: $userId");
        }

        $conn->begin_transaction();

        if ($datenschutzConsent) {
            $update_user_query_ds = "UPDATE users SET datenschutz_consent = ?, unterschrift = ? WHERE id = ?";
            $update_user_stmt_ds = $conn->prepare($update_user_query_ds);
            if (!$update_user_stmt_ds) { throw new Exception("Prepare failed (Datenschutz): " . $conn->error); }
            $update_user_stmt_ds->bind_param("isi", $datenschutzConsent, $unterschrift, $userId);
            if (!$update_user_stmt_ds->execute()) { throw new Exception("Execute failed (Datenschutz): " . $update_user_stmt_ds->error); }
            $update_user_stmt_ds->close();
        }

        if (isset($_POST['preSurveyResponses'])) {
            $preSurveyResponses = json_decode($_POST['preSurveyResponses'], true);
            if (is_array($preSurveyResponses)) {
                foreach ($preSurveyResponses as $questionId => $response) {
                    $delete_query_pre = "DELETE FROM pre_survey_responses WHERE user_id = ? AND question_id = ?";
                    $delete_stmt_pre = $conn->prepare($delete_query_pre);
                    if (!$delete_stmt_pre) { throw new Exception("Prepare failed (PreSurvey Delete): " . $conn->error); }
                    $delete_stmt_pre->bind_param("is", $userId, $questionId);
                    if (!$delete_stmt_pre->execute()) { throw new Exception("Execute failed (PreSurvey Delete): " . $delete_stmt_pre->error); }
                    $delete_stmt_pre->close();

                    $insert_pre_survey_query = "INSERT INTO pre_survey_responses (user_id, question_id, response) VALUES (?, ?, ?)";
                    $insert_pre_survey_stmt = $conn->prepare($insert_pre_survey_query);
                    if (!$insert_pre_survey_stmt) { throw new Exception("Prepare failed (PreSurvey Insert): " . $conn->error); }
                    $insert_pre_survey_stmt->bind_param("iss", $userId, $questionId, $response);
                    if (!$insert_pre_survey_stmt->execute()) { throw new Exception("Execute failed (PreSurvey Insert): " . $insert_pre_survey_stmt->error); }
                    $insert_pre_survey_stmt->close();
                }
            }
        }

        foreach ($_POST as $key => $value) {
            if (strpos($key, 'q') === 0 && strpos($key, '_blank') === false && strpos($key, '_marker') === false) {
                $responseValue = is_array($value) ? implode(',', $value) : $value;
                $conn->begin_transaction();
                try {
                    $delete_query = "DELETE FROM responses WHERE user_id = ? AND question_id = ? AND attempt = ?";
                    $delete_stmt = $conn->prepare($delete_query);
                    if (!$delete_stmt) { throw new Exception("Prepare failed (DELETE): " . $conn->error); }
                    $delete_stmt->bind_param("isi", $userId, $key, $attemptNumber);
                    if (!$delete_stmt->execute()) { throw new Exception("Execute failed (DELETE): " . $delete_stmt->error); }
                    $delete_stmt->close();

                    $insert_query = "INSERT INTO responses (user_id, question_id, response, attempt) VALUES (?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_query);
                    if (!$insert_stmt) { throw new Exception("Prepare failed (INSERT): " . $conn->error); }
                    $insert_stmt->bind_param("issi", $userId, $key, $responseValue, $attemptNumber);
                    if (!$insert_stmt->execute()) { throw new Exception("Execute failed (INSERT): " . $insert_stmt->error); }
                    $insert_stmt->close();
                    $conn->commit();
                } catch (Exception $db_error) {
                    $conn->rollback();
                    error_log("DATABASE ERROR for key $key: " . $db_error->getMessage());
                }
            }
        }

        foreach ($_POST as $key => $value) {
            if ($key === 't2_course_list' || $key === 't2_course_feedback' ||
                $key === 't2CourseListOpenEndedResponse' || $key === 't2_course_feedbackOpenEndedResponse' ||
                strpos($key, 'OpenEndedResponse') !== false) {

                $normalizedKey = str_replace('OpenEndedResponse', '', $key);
                if ($normalizedKey === 't2CourseList') $normalizedKey = 't2_course_list';

                $delete_oer_query = "DELETE FROM open_ended_responses WHERE user_id = ? AND question_key = ? AND attempt = ?";
                $delete_oer_stmt = $conn->prepare($delete_oer_query);
                if (!$delete_oer_stmt) { throw new Exception("Prepare failed (OER Delete): " . $conn->error); }
                $delete_oer_stmt->bind_param("isi", $userId, $normalizedKey, $attemptNumber);
                if (!$delete_oer_stmt->execute()) { throw new Exception("Execute failed (OER Delete): " . $delete_oer_stmt->error); }
                $delete_oer_stmt->close();

                if (!empty(trim($value))) {
                    $insert_oer_query = "INSERT INTO open_ended_responses (user_id, question_key, response, attempt) VALUES (?, ?, ?, ?)";
                    $insert_oer_stmt = $conn->prepare($insert_oer_query);
                    if (!$insert_oer_stmt) { throw new Exception("Prepare failed (OER Insert): " . $conn->error); }
                    $insert_oer_stmt->bind_param("issi", $userId, $normalizedKey, $value, $attemptNumber);
                    if (!$insert_oer_stmt->execute()) { throw new Exception("Execute failed (OER Insert): " . $insert_oer_stmt->error); }
                    $insert_oer_stmt->close();
                }
            }
        }

        $update_fields = ["is_complete = ?", "current_section = ?"];
        $update_params = [$isComplete, $currentSection];
        $update_types = "ii";

        if ($postedStartTimestamp) {
            $update_fields[] = "t{$attemptNumber}_startTimestamp = ?";
            $update_params[] = $postedStartTimestamp;
            $update_types .= "s";
        }

        if ($isComplete && $postedEndTimestamp) {
            $update_fields[] = "t{$attemptNumber}_endTimestamp = ?";
            $update_params[] = $postedEndTimestamp;
            $update_types .= "s";
        }

        $update_query_sql = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = ?";
        $update_params[] = $userId;
        $update_types .= "i";

        $update_stmt_main = $conn->prepare($update_query_sql);
        if (!$update_stmt_main) { throw new Exception("Prepare failed (Main User Update): " . $conn->error); }
        $update_stmt_main->bind_param($update_types, ...$update_params);
        if (!$update_stmt_main->execute()) { throw new Exception("Execute failed (Main User Update): " . $update_stmt_main->error); }
        $update_stmt_main->close();

        if ($isComplete) {
            require_once('calculate-scores.php');
            $responses_query = "SELECT question_id, response FROM responses WHERE user_id = ? AND attempt = ?";
            $responses_stmt = $conn->prepare($responses_query);
            if (!$responses_stmt) { throw new Exception("Prepare failed (Scores Fetch): " . $conn->error); }
            $responses_stmt->bind_param("ii", $userId, $attemptNumber);
            if (!$responses_stmt->execute()) { throw new Exception("Execute failed (Scores Fetch): " . $responses_stmt->error); }
            $responses_result = $responses_stmt->get_result();
            $responses_for_scores = [];
            while ($row = $responses_result->fetch_assoc()) {
                $responses_for_scores[$row['question_id']] = $row['response'];
            }
            $responses_stmt->close();
            $scores = calculateCompetencyScores($responses_for_scores);
            foreach ($scores as $category => $score) {
                $delete_score_query = "DELETE FROM scores WHERE user_id = ? AND category = ? AND attempt_number = ?";
                $delete_score_stmt = $conn->prepare($delete_score_query);
                if (!$delete_score_stmt) { throw new Exception("Prepare failed (Score Delete): " . $conn->error); }
                $delete_score_stmt->bind_param("isi", $userId, $category, $attemptNumber);
                if (!$delete_score_stmt->execute()) { throw new Exception("Execute failed (Score Delete): " . $delete_score_stmt->error); }
                $delete_score_stmt->close();

                $insert_score_query = "INSERT INTO scores (user_id, attempt_number, category, score) VALUES (?, ?, ?, ?)";
                $insert_score_stmt = $conn->prepare($insert_score_query);
                if (!$insert_score_stmt) { throw new Exception("Prepare failed (Score Insert): " . $conn->error); }
                $insert_score_stmt->bind_param("iisd", $userId, $attemptNumber, $category, $score);
                if (!$insert_score_stmt->execute()) { throw new Exception("Execute failed (Score Insert): " . $insert_score_stmt->error); }
                $insert_score_stmt->close();
            }
        }

        $conn->commit();
        echo json_encode(["ok" => true, "message" => "Data saved successfully"]);

    } catch (Exception $e) {
        if (isset($conn) && $conn->ping()) {
            $conn->rollback();
        }
        error_log("Error in save_data.php: " . $e->getMessage() . "\nStack trace:\n" . $e->getTraceAsString());
        echo json_encode(["ok" => false, "message" => "Error saving data: " . $e->getMessage()]);
    }
} else {
    echo json_encode(["ok" => false, "message" => "Invalid request method"]);
}
?>