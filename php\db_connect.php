<?php
declare(strict_types=1);

// --- Environment Detection ---
$is_local = false;
if (isset($_SERVER['HTTP_HOST'])) {
    $host = $_SERVER['HTTP_HOST'];
    // Add any other local hostnames/IPs if needed
    if ($host === 'localhost' || $host === '127.0.0.1') {
        $is_local = true;
    }
} elseif (php_sapi_name() === 'cli') {
    // Optional: Handle command-line execution if necessary
    // You might need a different way to detect environment for CLI scripts
    // For now, assume CLI might be local for testing
    // $is_local = true; // Uncomment if you run scripts via CLI locally
}

// --- DB Credentials ---
if ($is_local) {
    // --- LOCAL (XAMPP) Credentials ---
    // !!! IMPORTANT: REPLACE WITH YOUR ACTUAL LOCAL XAMPP DETAILS !!!
    $DB_HOST = 'localhost';         // Usually 'localhost' for XAMPP
    $DB_NAME = 'open_digi_db';      // Your local database name
    $DB_USER = 'root';              // Your local MySQL username (DEFAULT: root)
    $DB_PASS = '';                  // Your local MySQL password (DEFAULT: empty)
    // ------------------------------------
    error_reporting(E_ALL);         // Show all errors locally
    ini_set('display_errors', '1'); // Display errors locally
} else {
    // --- REMOTE (University Server) Credentials ---
    $DB_HOST = 'localhost';         // As provided in your original file
    $DB_NAME = 'open_digi_db';      // As provided
    $DB_USER = 'opendigi24';        // As provided
    $DB_PASS = 'XAWQlnVsBCd9yGj';   // As provided
    // ------------------------------------
    error_reporting(E_ALL);         // Report all errors remotely too (for logging)
    ini_set('display_errors', '0'); // BUT DON'T display errors to users remotely
    ini_set('log_errors', '1');     // Log errors remotely instead
    // Optional: Specify a log file path if needed
    // ini_set('error_log', '/path/to/your/php-error.log');
}
/* --------------------------------------------------------------------------- */

// --- Database Connection ---
$conn = new mysqli($DB_HOST, $DB_USER, $DB_PASS, $DB_NAME);

if ($conn->connect_errno) {
    // Generic error for users, detailed error logged or shown locally
    $error_message = 'Database connection failed. Please try again later.';
    $detailed_error = 'DB connection failed: (' . $conn->connect_errno . ') ' . $conn->connect_error;

    // Log the detailed error regardless of environment
    error_log($detailed_error);

    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'ok'      => false,
        'message' => $error_message,
        // Optionally include detailed error only if local (for debugging)
        // 'details' => $is_local ? $detailed_error : null
    ]);
    exit;
}

// --- Set Character Set ---
if (!$conn->set_charset('utf8mb4')) {
    // Log error if setting charset fails
    error_log("Error loading character set utf8mb4: " . $conn->error);
    // You might want to handle this more gracefully depending on requirements
}

?>