# Comprehensive Codebook & Developer Documentation for the Open-Digi Survey Website

**Purpose of this Document:** This codebook serves as the primary technical reference for developers maintaining or extending the Open-Digi Survey Website. It details the project's architecture, components, setup, key functionalities, and maintenance procedures.

## Table of Contents

1.  [Project Overview](#1-project-overview)
    - [1.1 Purpose & Research Context](#11-purpose--research-context)
    - [1.2 Key Features & User Flow](#12-key-features--user-flow)
    - [1.3 Project Contacts](#13-project-contacts)
2.  [System Architecture](#2-system-architecture)
    - [2.1 High-Level Diagram](#21-high-level-diagram)
    - [2.2 Component Interaction & Data Flow](#22-component-interaction--data-flow)
3.  [Technology Stack](#3-technology-stack)
    - [3.1 Frontend Technologies](#31-frontend-technologies)
    - [3.2 Backend Technologies](#32-backend-technologies)
    - [3.3 Database](#33-database)
    - [3.4 Development Tools & Environment](#34-development-tools--environment)
4.  [Development Environment Setup](#4-development-environment-setup)
    - [4.1 Prerequisites](#41-prerequisites)
    - [4.2 Step-by-Step Local Setup Guide](#42-step-by-step-local-setup-guide)
    - [4.3 Common Setup Issues](#43-common-setup-issues)
5.  [Database Structure](#5-database-structure)
    - [5.1 Entity Relationship Diagram (ERD)](#51-entity-relationship-diagram-erd)
    - [5.2 Detailed Table Schema](#52-detailed-table-schema)
    - [5.3 Key Relationships & Data Integrity](#53-key-relationships--data-integrity)
    - [5.4 Important Data Notes (Groups, Attempts)](#54-important-data-notes-groups-attempts)
6.  [Backend API Documentation (PHP Endpoints)](#6-backend-api-documentation-php-endpoints)
    - [6.1 Overview & Conventions](#61-overview--conventions)
    - [6.2 Authentication Endpoints (`register.php`, `login.php`)](#62-authentication-endpoints-registerphp-loginphp)
    - [6.3 Survey Data Endpoints (`save_data.php`, `save-open-ended-response.php`)](#63-survey-data-endpoints-savedataphp-save-open-ended-responsephp)
    - [6.4 Data Retrieval Endpoints (`get_user_data.php`, `check-results.php`)](#64-data-retrieval-endpoints-getuserdataphp-check-resultsphp)
    - [6.5 Score Calculation (`calculate-scores.php`)](#65-score-calculation-calculate-scoresphp)
    - [6.6 Admin Endpoints (`dashboard-login.php`, `dashboard-data.php`)](#66-admin-endpoints-dashboard-loginphp-dashboard-dataphp)
7.  [Frontend Structure](#7-frontend-structure)
    - [7.1 HTML Page Structure & Purpose](#71-html-page-structure--purpose)
    - [7.2 CSS Styling Strategy](#72-css-styling-strategy)
    - [7.3 JavaScript Architecture & Key Files](#73-javascript-architecture--key-files)
    - [7.4 Client-Side State Management (`sessionStorage`)](#74-client-side-state-management-sessionstorage)
8.  [Key Functionality Documentation](#8-key-functionality-documentation)
    - [8.1 User Code Generation & Login Flow](#81-user-code-generation--login-flow)
    - [8.2 Survey Engine (`survey.min.js` Deep Dive)](#82-survey-engine-surveyminjs-deep-dive)
    - [8.3 Group-Specific Logic (A, B, C, D)](#83-group-specific-logic-a-b-c-d)
    - [8.4 Score Calculation & Display](#84-score-calculation--display)
    - [8.5 Results Page & PDF Export](#85-results-page--pdf-export)
    - [8.6 Admin Dashboard Functionality](#86-admin-dashboard-functionality)
9.  [Code Style Guide](#9-code-style-guide)
    - [9.1 Naming Conventions (HTML, CSS, JS, PHP)](#91-naming-conventions-html-css-js-php)
    - [9.2 Formatting Practices](#92-formatting-practices)
    - [9.3 Commenting Standards](#93-commenting-standards)
    - [9.4 Project-Specific Patterns](#94-project-specific-patterns)
10. [Maintenance and Deployment Guide](#10-maintenance-and-deployment-guide)
    - [10.1 Common Maintenance Tasks (Adding Questions, Modifying Logic)](#101-common-maintenance-tasks-adding-questions-modifying-logic)
    - [10.2 Database Updates & Migrations](#102-database-updates--migrations)
    - [10.3 Deployment Checklist (Local -> Production)](#103-deployment-checklist-local---production)
    - [10.4 Backup Procedures (Code & Database)](#104-backup-procedures-code--database)
11. [Common Issues and Troubleshooting](#11-common-issues-and-troubleshooting)
    - [11.1 Debugging Tools & Techniques (Browser, PHP Logs)](#111-debugging-tools--techniques-browser-php-logs)
    - [11.2 Specific Problem Scenarios & Solutions](#112-specific-problem-scenarios--solutions)
12. [Security Considerations](#12-security-considerations)
    - [12.1 Current Measures & Identified Vulnerabilities](#121-current-measures--identified-vulnerabilities)
    - [12.2 Recommended Security Hardening Steps](#122-recommended-security-hardening-steps)
13. [Future Enhancement Opportunities](#13-future-enhancement-opportunities)
    - [13.1 Technical Debt & Refactoring Ideas](#131-technical-debt--refactoring-ideas)
    - [13.2 Potential New Features](#132-potential-new-features)
14. [Glossary of Terms](#14-glossary-of-terms)
15. [Technical Debt Tracking](#15-technical-debt-tracking)
16. [File Structure Map](#16-file-structure-map)
17. [Implementation Guide: Adding a New Survey Question](#17-implementation-guide-adding-a-new-survey-question)

---

## 1. Project Overview

### 1.1 Purpose & Research Context

The Open-Digi Survey Website is a critical component of the **Open-Digi research project** conducted at the University of Rostock. The primary research goal is to understand the effectiveness of personalized, asynchronous micro-training interventions in developing digital competencies among teacher education students.

This website facilitates the research by:

- **Recruiting and Identifying Participants:** Allowing students to generate a unique, anonymous code.
- **Collecting Longitudinal Data:** Administering surveys at three distinct time points (T1, T2, T3) to measure changes in self-perceived digital competencies.
- **Implementing Experimental Conditions:** Assigning users to different groups (A, B, C, D) which may receive different feedback types or follow different procedural paths within the survey/results flow.
- **Providing Feedback:** Displaying calculated competency scores and visualizations to participants (depending on their group).
- **Gathering Qualitative Data:** Collecting open-ended reflections on learning strategies and experiences.
- **Enabling Data Analysis:** Providing researchers with an administrative dashboard to view and export the collected (anonymized) data.

The website's live URL is: `http://open-digi.uni-rostock.de/`

### 1.2 Key Features & User Flow

1.  **Code Generation:** New users provide specific personal (but non-identifying) details (birthplace initials, parent initial, birthday, school initials) on `generateCode.html` to create a unique code (format XX-XX-XX-XX). This code is their persistent identifier for all survey attempts.
2.  **Login:** Users access `login.html`, indicate if they have participated before, choose an action (start new, continue, redo T2, follow-up T3, view results), and enter their code.
3.  **Survey Participation (`survey.html`):**
    - Users are presented with survey sections sequentially.
    - **T1:** Includes Pre-Survey (participation/group assignment), Datenschutz (consent), Personal Info, and Competency questions.
    - **T2/T3:** Typically start with Personal Info (mostly locked/pre-filled) followed by Competency questions. T2 includes specific course feedback questions for certain groups.
    - Progress is saved section-by-section via `php/save_data.php`.
    - Client-side validation ensures required questions are answered.
4.  **Results Display (`survey.html` completion / `results.html`):**
    - Upon completing an attempt, scores are calculated (`php/calculate-scores.php`).
    - Results (scores, charts) and reflection prompts are displayed dynamically based on the user's group (A/B/C/D) and attempt number (T1/T2/T3).
    - Users can view past results via `login.html` -> "View Results" -> `results.html`.
    - PDF export of results is available on `results.html`.
5.  **Admin Dashboard (`dashboard.html`):**
    - Requires login via `dashboard-login.html`.
    - Displays a sortable, filterable table of all user data.
    - Allows data export to CSV.
    - Provides visualization of individual user scores over time.

### 1.3 Project Contacts

- **Project Lead / Academic Contact:** Jun.-Prof.in Dr.in Charlott Rubach (`<EMAIL>`), Anne-Kathrin Hirsch (`<EMAIL>`)
- **Initial Website Developer:** Kenan Trivedi (`<EMAIL>`)
- **Code Repository:** `[Link to Repository if applicable]`

---

## 2. System Architecture

### 2.1 High-Level Diagram

The system employs a traditional client-server model:

```

+-----------------+ +----------------------+ +---------------------+
| | HTTP | | SQL | |
| User Browser |----->| Web Server (Apache) |----->| Database Server |
| (HTML/CSS/JS) |<-----| (PHP Processor) |<-----| (MariaDB/MySQL) |
| - Renders UI | | - Executes PHP | | - Stores Data |
| - Runs JS Logic | | - Handles Requests | | - Executes Queries |
| - Sends Requests| | - Connects to DB | | |
+-----------------+ +----------------------+ +---------------------+

```

### 2.2 Component Interaction & Data Flow

1.  **Request Initiation:** The user interacts with an HTML page in their browser (e.g., fills a form, clicks a button).
2.  **Client-Side Processing (JavaScript):** JavaScript (`script.min.js`, `survey.min.js`, etc.) handles the interaction. This may involve:
    - Validating form input.
    - Dynamically updating the UI (e.g., showing/hiding elements, rendering survey questions).
    - Preparing data to be sent to the server.
    - Making an asynchronous request (using `fetch`) to a specific PHP endpoint (e.g., `php/save_data.php`).
3.  **Server-Side Processing (PHP):**
    - The web server (e.g., Apache) receives the request and directs it to the appropriate PHP script based on the URL.
    - The PHP script executes. This typically involves:
      - Connecting to the database (using credentials from `php/db_connect.php`).
      - Sanitizing and validating received data (from `$_POST`, `$_GET`, or JSON body).
      - Performing business logic (e.g., checking login credentials, calculating scores, determining user group).
      - Executing SQL queries against the database (e.g., `SELECT`, `INSERT`, `UPDATE`).
      - Processing data retrieved from the database.
      - Formatting a response (usually as JSON).
4.  **Database Interaction (MariaDB/MySQL):** The database server executes the SQL queries sent by PHP, retrieving or modifying data in the tables (`users`, `responses`, etc.).
5.  **Response Transmission:** The PHP script sends its response (e.g., JSON data like `{"ok": true, "userId": 123}` or an HTML snippet if it were rendering server-side) back to the browser via the web server.
6.  **Client-Side Response Handling (JavaScript):** The JavaScript `fetch` call receives the response.
    - It checks if the request was successful (e.g., HTTP status 200, `ok: true` in JSON).
    - It processes the received data (e.g., stores `userId` in `sessionStorage`, updates the UI with results, displays success/error messages using SweetAlert2).
    - It may trigger further actions (e.g., redirecting to another page, rendering the next survey section).

---

## 3. Technology Stack

This project intentionally uses a simple, traditional web stack without heavy frameworks to ensure ease of deployment in standard university server environments and maintainability without requiring specialized framework knowledge.

### 3.1 Frontend Technologies

- **HTML5:** Used for structuring the web pages. Standard semantic elements are preferred.
- **CSS3:** Used for styling.
  - **Global Styles:** `css/styles.css` defines base styles, header, footer, typography, and common button styles.
  - **Page-Specific Styles:** Files like `css/survey.css`, `css/login.css`, `css/dashboard.css` contain styles tailored to individual pages or components.
  - **CSS Variables:** Defined in `:root` (primarily in `styles.css`) for theme colors (e.g., `--primary-color`), fonts, etc., allowing easier visual adjustments.
- **Vanilla JavaScript (ES6+):** All client-side interactivity and dynamic behavior are implemented using plain JavaScript. No frameworks like React, Vue, or Angular are used. Key features used include `fetch` API for backend communication, DOM manipulation, event handling, and `sessionStorage` for state.
- **External Libraries (via CDN):**
  - **Chart.js (v3.x recommended):** Renders bar charts for competency scores on `results.html` and `dashboard.html`. Configuration is handled in `script.min.js` and `survey.min.js`.
  - **SweetAlert2:** Provides user-friendly, customizable pop-up messages for success, errors, and confirmations, replacing standard browser alerts.
  - **html2canvas & jsPDF:** Work together on `results.html` to capture the results section as an image (`html2canvas`) and embed it into a downloadable PDF (`jsPDF`).
  - **canvas-confetti:** Used for a simple visual celebration effect when users successfully complete survey attempts (triggered in `survey.min.js`).
  - **Flatpickr:** A lightweight date/time picker library used _only_ on the admin dashboard (`dashboard.html`) for selecting date ranges for filtering data.
  - **Font Awesome:** Provides scalable vector icons used in buttons and UI elements.
  - **Google Fonts (Roboto):** The primary font family used throughout the website for consistent typography.

### 3.2 Backend Technologies

- **PHP:** Version 8.0 or higher is recommended for performance and security benefits. The code is primarily procedural, with functions grouped logically within files in the `/php` directory. It handles all server-side logic, database interaction, and API responses.
  - **Required Extensions:** `mysqli` (for database connection), `json` (for handling JSON requests/responses), `mbstring` (for multi-byte string operations, good practice), `xml` (potentially used by dependencies or future features).

### 3.3 Database

- **MariaDB / MySQL:** A relational database used for storing all persistent data. The specific choice between MariaDB and MySQL depends on the server environment; the application is compatible with both. The default database name is `open_digi_db`.

### 3.4 Development Tools & Environment

- **Local Server Stack:** XAMPP (Windows/Mac/Linux), MAMP (Mac), WAMP (Windows), or a native LAMP/LEMP stack is required for local development to run PHP and MySQL.
- **Text Editor/IDE:** Any code editor works (VS Code, Sublime Text, PhpStorm, Atom, etc.).
- **Web Browser:** Modern browser (Chrome, Firefox, Edge, Safari) with built-in Developer Tools (F12) for debugging JS, inspecting HTML/CSS, and monitoring network requests.
- **Database Client:** phpMyAdmin (usually included with XAMPP/MAMP/WAMP) or a standalone client like MySQL Workbench, DBeaver, or Sequel Pro/Ace for database management and querying.
- **Version Control (Recommended):** Git for tracking code changes, collaboration, and deployment. A remote repository (GitHub, GitLab, etc.) is highly recommended for backups and team access.

---

## 4. Development Environment Setup

### 4.1 Prerequisites

- **Local Web Server:** Install XAMPP, MAMP, WAMP, or set up a native LAMP/LEMP stack. Ensure Apache/Nginx, MySQL/MariaDB, and PHP (v8.0+) are running.
- **PHP Extensions:** Verify that `mysqli`, `json`, `mbstring`, and `xml` extensions are enabled in your `php.ini` configuration file. You might need to uncomment lines like `extension=mysqli` and restart your web server.
- **Database Access:** Know how to access your database management tool (e.g., phpMyAdmin at `http://localhost/phpmyadmin`).
- **Project Files:** Obtain the complete project code (either via Git clone or downloading a zip archive).
- **Database Schema File:** Have the `open_digi_db.sql` file available.

### 4.2 Step-by-Step Local Setup Guide

1.  **Place Project Files:**

    - Copy or clone the entire `open-digi-survey-website` project folder into your web server's document root directory.
      - XAMPP: Usually `C:\xampp\htdocs\` or `/Applications/XAMPP/htdocs/`.
      - MAMP: Usually `/Applications/MAMP/htdocs/`.
      - WAMP: Usually `C:\wamp\www\`.
      - Native Linux: Often `/var/www/html/`.

2.  **Create Database:**

    - Open phpMyAdmin in your browser.
    - Click on the "Databases" tab.
    - In the "Create database" field, enter `open_digi_db`.
    - Select a collation like `utf8mb4_general_ci` (or `utf8mb4_unicode_ci`).
    - Click "Create".

3.  **Import Database Schema:**

    - Click on the newly created `open_digi_db` database name in the left-hand sidebar of phpMyAdmin to select it.
    - Click on the "Import" tab at the top.
    - Under "File to import", click "Choose File" and locate the `open_digi_db.sql` file from the project.
    - Ensure the character set is `utf8mb4`.
    - Leave other options as default unless you know otherwise.
    - Click the "Go" or "Import" button at the bottom. Wait for the success message indicating the tables were created.

4.  **Verify Tables:**

    - After successful import, click on the `open_digi_db` database name again. You should see the tables listed: `users`, `responses`, `scores`, `pre_survey_responses`, `open_ended_responses`, `dashboard_users`.

5.  **Configure Database Connection (`db_connect.php`):**

    - Open the file `/php/db_connect.php` within your project folder in your text editor.
    - Review the connection variables:
      ```php
      $host = "localhost"; // Usually correct for local setups
      $username = "root";     // Default XAMPP/MAMP username, might be different
      $password = "";         // Default XAMPP/MAMP password (empty), might be 'root' for MAMP or set during install
      $database = "open_digi_db"; // Should match the database you created
      ```
    - Adjust `$username` and `$password` if your local MySQL setup uses different credentials. Save the file.

6.  **Check Dashboard User:**

    - In phpMyAdmin, select the `open_digi_db` database, then click on the `dashboard_users` table.
    - Click the "Browse" tab. Ensure there is at least one row (e.g., `user_id` = 'opendigi', `password` = 'opendigi123').
    - If the table is empty, go to the "SQL" tab and run the insert command:
      ```sql
      INSERT INTO `dashboard_users` (`user_id`, `password`) VALUES ('opendigi', 'opendigi123');
      ```
      **(Remember this password is plain text!)**

7.  **Test the Application:**
    - Open your web browser and navigate to the project's URL on your local server (e.g., `http://localhost/open-digi-survey-website/`).
    - The landing page (`index.html`) should load without errors.
    - Try generating a code, logging in, and accessing the dashboard login page (`dashboard-login.html`).

### 4.3 Common Setup Issues

- **"Connection failed" errors / Blank Pages:** Usually indicates incorrect database credentials in `php/db_connect.php` or the MySQL server not running. Double-check credentials and ensure MySQL is active in your XAMPP/MAMP/WAMP control panel.
- **"Table 'open_digi_db.users' doesn't exist" errors:** The database import likely failed or wasn't performed. Re-import the `open_digi_db.sql` file carefully.
- **PHP Errors Displayed:** If you see raw PHP errors, ensure `display_errors` is Off in your production `php.ini`, but you might need it On locally for debugging (check your XAMPP/MAMP PHP settings). Ensure required PHP extensions (`mysqli`, `json`) are enabled.
- **CSS/JS Not Loading (404 Errors):** Check the paths in your HTML files (`<link href="...">`, `<script src="...">`). Ensure they correctly point to the `css/` and `js/` folders relative to the HTML file's location. Check file/folder permissions if on Linux/Mac.
- **XAMPP Apache Port Conflicts:** If Apache doesn't start, another application (like Skype) might be using port 80. Configure Apache to use a different port (e.g., 8080) in its configuration files (`httpd.conf`) and access the site via `http://localhost:8080/...`.

---

## 5. Database Structure

### 5.1 Entity Relationship Diagram (ERD)

_(See ASCII diagram in Section 5.2 for relationships)_

### 5.2 Detailed Table Schema

_(Based on the provided database dump)_

#### 1. `dashboard_users`

Stores credentials for accessing the administrative dashboard.

| Column     | Type           | Null | Default | Attributes      | Description                                                                              |
| :--------- | :------------- | :--- | :------ | :-------------- | :--------------------------------------------------------------------------------------- |
| `id`       | `int(11)`      | No   |         | Primary Key, AI | Unique identifier for the admin user record.                                             |
| `user_id`  | `varchar(50)`  | No   |         |                 | Username used for dashboard login (e.g., "opendigi").                                    |
| `password` | `varchar(255)` | No   |         |                 | **SECURITY RISK:** Admin password, currently stored as plain text. **Should be hashed.** |

#### 2. `users`

The central table storing information about each survey participant.

| Column                | Type           | Null | Default | Attributes      | Description                                                                                                                                                            |
| :-------------------- | :------------- | :--- | :------ | :-------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                  | `int(11)`      | No   |         | Primary Key, AI | Unique identifier for each participant. Used as foreign key in other tables.                                                                                           |
| `user_code`           | `varchar(15)`  | No   |         | Unique Index    | The unique XX-XX-XX-XX code generated by/for the user. Used for login.                                                                                                 |
| `attempt_number`      | `int(11)`      | Yes  | `1`     |                 | Tracks the current survey attempt (1, 2, or 3) the user is actively taking or has last completed.                                                                      |
| `is_complete`         | `tinyint(1)`   | Yes  | `0`     |                 | Flag (0 or 1) indicating if the _current_ `attempt_number` was fully submitted. _Note: Check timestamps or scores table for definitive completion status per attempt._ |
| `current_section`     | `int(11)`      | Yes  | `-2`    |                 | Index of the last survey section the user accessed. Used to resume progress. (-2=PreSurvey, -1=Datenschutz, 0 to N-1 = Survey Sections).                               |
| `datenschutz_consent` | `tinyint(1)`   | Yes  | `0`     |                 | Flag (0 or 1) indicating user consent to the data protection policy (given in T1).                                                                                     |
| `unterschrift`        | `varchar(255)` | Yes  | `NULL`  |                 | Stores the `user_code` as a digital signature upon consent.                                                                                                            |
| `t1_timestamp`        | `datetime`     | Yes  | `NULL`  |                 | Timestamp recorded when attempt 1 was marked as complete.                                                                                                              |
| `t2_timestamp`        | `datetime`     | Yes  | `NULL`  |                 | Timestamp recorded when attempt 2 was marked as complete.                                                                                                              |
| `t3_timestamp`        | `datetime`     | Yes  | `NULL`  |                 | Timestamp recorded when attempt 3 was marked as complete.                                                                                                              |

#### 3. `responses`

Stores individual answers to the main survey questions for each attempt.

| Column        | Type          | Null | Default | Attributes      | Description                                                                                                      |
| :------------ | :------------ | :--- | :------ | :-------------- | :--------------------------------------------------------------------------------------------------------------- |
| `id`          | `int(11)`     | No   |         | Primary Key, AI | Unique identifier for each response record.                                                                      |
| `user_id`     | `int(11)`     | No   |         | Index           | Foreign key referencing `users.id`.                                                                              |
| `question_id` | `varchar(50)` | No   |         | Index           | Identifier for the specific question (e.g., "q0_0", "q1_0", "q0_7"). Links conceptually to `survey-data.min.js`. |
| `response`    | `text`        | Yes  | `NULL`  |                 | The user's answer, stored as text (e.g., "4", "Ja", "facher group b", "1995"). Needs parsing for scores.         |
| `attempt`     | `int(11)`     | Yes  | `1`     | Index           | The survey attempt number (1, 2, or 3) this response belongs to.                                                 |

#### 4. `pre_survey_responses`

Stores answers to the initial participation and group assignment questions (asked only in T1).

| Column        | Type          | Null | Default | Attributes      | Description                                                           |
| :------------ | :------------ | :--- | :------ | :-------------- | :-------------------------------------------------------------------- |
| `id`          | `int(11)`     | No   |         | Primary Key, AI | Unique identifier for the pre-survey response record.                 |
| `user_id`     | `int(11)`     | No   |         | Index           | Foreign key referencing `users.id`.                                   |
| `question_id` | `varchar(50)` | No   |         |                 | Identifier for the pre-survey question (e.g., "q-2_0", "q-2_1").      |
| `response`    | `text`        | Yes  | `NULL`  |                 | The user's answer (e.g., "Ja", "Nein", "Gruppe A", "Gruppe B", etc.). |

#### 5. `open_ended_responses`

Stores the free-text responses entered by users in reflection textareas.

| Column         | Type          | Null | Default | Attributes      | Description                                                                                             |
| :------------- | :------------ | :--- | :------ | :-------------- | :------------------------------------------------------------------------------------------------------ |
| `id`           | `int(11)`     | No   |         | Primary Key, AI | Unique identifier for the open-ended response record.                                                   |
| `user_id`      | `int(11)`     | No   |         | Index           | Foreign key referencing `users.id`.                                                                     |
| `question_key` | `varchar(50)` | No   |         | Index           | Identifier for the specific reflection prompt (e.g., "t1_strategy", "t2_reflection", "t2_course_list"). |
| `response`     | `text`        | Yes  | `NULL`  |                 | The full text entered by the user.                                                                      |
| `attempt`      | `int(11)`     | Yes  | `1`     | Index           | The survey attempt number (1, 2, or 3) during which this reflection was provided.                       |

#### 6. `scores`

Stores the calculated competency scores generated after each survey attempt is completed.

| Column           | Type           | Null | Default | Attributes      | Description                                                                                   |
| :--------------- | :------------- | :--- | :------ | :-------------- | :-------------------------------------------------------------------------------------------- |
| `id`             | `int(11)`      | No   |         | Primary Key, AI | Unique identifier for the score record.                                                       |
| `user_id`        | `int(11)`      | No   |         | Index           | Foreign key referencing `users.id`.                                                           |
| `attempt_number` | `int(11)`      | No   |         | Index           | The survey attempt number (1, 2, or 3) these scores correspond to.                            |
| `category`       | `varchar(100)` | No   |         | Index           | The name of the competency category (e.g., "Suchen, Verarbeiten und Aufbewahren", "overall"). |
| `score`          | `double`       | No   |         |                 | The calculated score for the category and attempt, typically on a 0-100 scale.                |

_(AI = Auto Increment)_

### 5.3 Key Relationships & Data Integrity

- **`users` is the central table.** All other data tables (`responses`, `scores`, `pre_survey_responses`, `open_ended_responses`) link back to `users.id` via a `user_id` foreign key.
- **Attempts:** Data related to different survey attempts (T1, T2, T3) is distinguished by the `attempt` column in `responses` and `open_ended_responses`, and the `attempt_number` column in `scores`. The `users.attempt_number` indicates the _current_ attempt being worked on or last completed.
- **Data Deletion:** Currently, there are no `ON DELETE CASCADE` constraints defined. Deleting a user from the `users` table would leave orphaned records in other tables. Manual cleanup or adding constraints would be needed if user deletion is required.
- **Indexing:** Indexes are present on `user_id`, `question_id`, `attempt`, `question_key`, `category`, and `attempt_number` in the respective tables to improve query performance, especially for `get_user_data.php` and `dashboard-data.php`.

### 5.4 Important Data Notes (Groups, Attempts)

- **User Group:** A user's assigned group (A, B, C, or D) is determined by their answer to question `q-2_1` stored in the `pre_survey_responses` table (recorded during T1). If they answered "Nein" to `q-2_0`, they are implicitly Group A. This group assignment persists for all attempts and dictates the logic flow in `survey.min.js` (especially in `showResults`).
- **Attempt Number vs. Timestamps:**
  - `users.attempt_number` indicates the _current_ attempt.
  - `scores.attempt_number` and `responses.attempt` link data to a specific attempt (1, 2, or 3).
  - `users.t1_timestamp`, `t2_timestamp`, `t3_timestamp` provide definitive proof that a specific attempt was _completed_. Use these timestamps to reliably determine which attempts a user has finished.

---

## 6. Backend API Documentation (PHP Endpoints)

All backend endpoints are PHP scripts located in the `/php` directory. They are accessed directly via HTTP requests from the frontend JavaScript.

### 6.1 Overview & Conventions

- **Request Method:** Primarily `POST` for actions that modify data (login, register, save) and `GET` for data retrieval.
- **Data Format:**
  - `POST` requests typically send data as `application/x-www-form-urlencoded` (using `FormData` in JS) or `application/json` (for specific endpoints like `save-open-ended-response.php`).
  - Responses are almost always `application/json`.
- **Authentication:** User endpoints rely on the `userId` (obtained after login/registration and stored in `sessionStorage`) being passed correctly. Dashboard endpoints require a Bearer token in the `Authorization` header.
- **Error Handling:** Scripts generally return a JSON object with an `ok: false` or `success: false` flag and a `message` field upon error. HTTP status codes (like 401, 500) may also be used. Check PHP error logs for detailed backend errors.
- **Database Connection:** All scripts requiring database access include `php/db_connect.php`.

### 6.2 Authentication Endpoints (`register.php`, `login.php`)

#### `php/register.php`

- **Purpose:** Registers a new user based on generated code components.
- **Method:** `POST`
- **Request Body:** `application/x-www-form-urlencoded` (from FormData)
  - `code` (string, required): The generated user code (e.g., "GW-SI-06-KE").
- **Process:**
  1.  Retrieves `code`.
  2.  Connects to DB.
  3.  Checks if `code` already exists in `users` table (case-insensitive).
  4.  If duplicate, returns `{"ok": false, "isDuplicateCode": true, ...}`.
  5.  If unique, `INSERT`s a new row into `users` with the `user_code`.
  6.  Retrieves the newly generated `id` (`insert_id`).
  7.  Returns `{"ok": true, "userId": [new_id], "message": "..."}`.
- **Success Response:** `200 OK`, JSON `{"ok": true, "userId": 123, "message": "Registration successful"}`
- **Error Response:** `200 OK`, JSON `{"ok": false, "isDuplicateCode": true/false, "message": "Error description"}`

#### `php/login.php`

- **Purpose:** Authenticates an existing user and sets up their session attempt.
- **Method:** `POST`
- **Request Body:** `application/x-www-form-urlencoded` (from FormData)
  - `code` (string, required): User's code.
  - `surveyOption` (string, required): Action selected (e.g., "continue", "redo", "followup").
  - `startNewAttempt` (string "true"/"false", optional): Indicates if a new attempt (T2/T3) should be initiated.
  - `attemptType` (string "T2"/"T3", optional): Specifies which attempt to start if `startNewAttempt` is true.
- **Process:**
  1.  Retrieves parameters.
  2.  Connects to DB.
  3.  `SELECT`s `id`, `attempt_number` from `users` where `user_code` matches (case-insensitive).
  4.  If user not found, returns `{"ok": false, "message": "Invalid code"}`.
  5.  If user found:
      - If `startNewAttempt` is true:
        - Determines the new `attemptNumber` (2 for "T2", 3 for "T3").
        - `UPDATE`s the `users` table, setting the `attempt_number` to the new value for the user's `id`.
      - If `startNewAttempt` is false (or not set): Uses the existing `attempt_number` from the database.
  6.  Returns `{"ok": true, "userId": [user_id], "attemptNumber": [determined_attempt_number], "message": "..."}`.
- **Success Response:** `200 OK`, JSON `{"ok": true, "userId": 123, "attemptNumber": 2, "message": "Login successful"}`
- **Error Response:** `200 OK`, JSON `{"ok": false, "message": "Invalid code"}` or other error.

### 6.3 Survey Data Endpoints (`save_data.php`, `save-open-ended-response.php`)

#### `php/save_data.php`

- **Purpose:** Saves survey progress (section answers) or marks an attempt as complete, triggering score calculation.
- **Method:** `POST`
- **Request Body:** `application/x-www-form-urlencoded` (from FormData)
  - `userId` (int, required)
  - `attemptNumber` (int, required)
  - `currentSection` (int, required)
  - `isComplete` (string "true"/"false", required)
  - `datenschutzConsent` (string "true"/"false", optional)
  - `unterschrift` (string, optional)
  - `preSurveyResponses` (JSON string, optional)
  - `qX_Y` (string): Multiple parameters representing answers to survey questions.
  - `{key}OpenEndedResponse` / `t2_course_list` / `t2_course_feedback` (string, optional): Open-ended answers submitted with the main form.
- **Process:**
  1.  Retrieves all parameters.
  2.  Connects to DB, starts transaction.
  3.  Validates `userId`.
  4.  If `datenschutzConsent` present, `UPDATE`s `users` table.
  5.  If `preSurveyResponses` present, decodes JSON and saves/updates rows in `pre_survey_responses` table (DELETE then INSERT).
  6.  Iterates through `qX_Y` parameters, saves/updates rows in `responses` table for the given `userId` and `attemptNumber` (DELETE then INSERT).
  7.  Iterates through open-ended response parameters, saves/updates rows in `open_ended_responses` table (DELETE then INSERT).
  8.  `UPDATE`s `users` table with `current_section` and `is_complete` status.
  9.  If `isComplete` is true:
      - Sets the appropriate timestamp (`t1_timestamp`, `t2_timestamp`, or `t3_timestamp`) in the `users` table `UPDATE` query.
      - Includes `php/calculate-scores.php`.
      - Fetches all responses for the current `userId` and `attemptNumber` from the `responses` table.
      - Calls `calculateCompetencyScores()` with the fetched responses.
      - Iterates through the returned scores, saving/updating rows in the `scores` table (DELETE then INSERT).
  10. Commits transaction.
  11. Returns `{"ok": true, "message": "..."}`.
  12. If any error occurs, rolls back transaction and returns `{"ok": false, "message": "..."}`.
- **Success Response:** `200 OK`, JSON `{"ok": true, "message": "Data saved successfully"}`
- **Error Response:** `200 OK` or `500 Internal Server Error`, JSON `{"ok": false, "message": "Error saving data: ..."}`

#### `php/save-open-ended-response.php`

- **Purpose:** Saves specific reflection texts submitted independently at the end of survey attempts.
- **Method:** `POST`
- **Request Body:** `application/json`
  - `userId` (int, required)
  - `key` (string, required): Identifier like "t1_strategy", "t2_reflection".
  - `response` (string, required): The text content.
  - `attempt` (int, required)
- **Process:**
  1.  Decodes JSON body.
  2.  Connects to DB, starts transaction.
  3.  Validates input.
  4.  Saves/updates the response in the `open_ended_responses` table for the given `userId`, `key`, and `attempt` (DELETE then INSERT).
  5.  Commits transaction.
  6.  Returns `{"success": true, "message": "..."}`.
  7.  If error, rolls back and returns `{"success": false, "message": "..."}`.
- **Success Response:** `200 OK`, JSON `{"success": true, "message": "Response saved successfully"}`
- **Error Response:** `200 OK` or `500 Internal Server Error`, JSON `{"success": false, "message": "Error: ..."}`

### 6.4 Data Retrieval Endpoints (`get_user_data.php`, `check-results.php`)

#### `php/get_user_data.php`

- **Purpose:** Fetches all relevant data for a single user, used for resuming surveys and displaying results.
- **Method:** `GET`
- **URL Parameters:**
  - `userId` (int, required): The ID of the user whose data is needed.
- **Process:**
  1.  Retrieves `userId` from `$_GET`.
  2.  Connects to DB.
  3.  `SELECT`s user details from `users` table.
  4.  `SELECT`s all responses for the user from `responses` table.
  5.  `SELECT`s all scores for the user from `scores` table.
  6.  `SELECT`s all pre-survey responses for the user from `pre_survey_responses` table.
  7.  `SELECT`s all open-ended responses for the user from `open_ended_responses` table.
  8.  Aggregates all retrieved data into a structured associative array (grouping responses/scores by attempt).
  9.  Returns the aggregated data as a JSON object.
- **Success Response:** `200 OK`, JSON object containing comprehensive user data (see structure in Section 7.3 description for `survey.min.js` `loadUserData`).
- **Error Response:** `200 OK` or `500 Internal Server Error`, JSON `{"ok": false, "message": "User not found..."}` or other DB error.

#### `php/check-results.php`

- **Purpose:** Checks if results are available for a user code (used on `login.html`).
- **Method:** `POST`
- **Request Body:** `application/json`
  - `code` (string, required): The user code to check.
- **Process:**
  1.  Decodes JSON body.
  2.  Connects to DB.
  3.  `SELECT`s `id`, `is_complete` from `users` where `user_code` matches.
  4.  If user not found, returns `{"success": false, "message": "Code nicht gefunden"}`.
  5.  Checks if the user has completed at least one attempt (e.g., by checking `is_complete` flag or if scores exist for attempt 1 - _exact logic might need verification in the script_).
  6.  If results available, returns `{"success": true, "userId": [user_id]}`.
  7.  If no results available, returns `{"success": false, "message": "Keine Ergebnisse verfügbar"}`.
- **Success Response:** `200 OK`, JSON `{"success": true, "userId": 123}`
- **Error Response:** `200 OK`, JSON `{"success": false, "message": "Error description"}`

### 6.5 Score Calculation (`calculate-scores.php`)

- **Purpose:** Contains the logic to calculate competency scores based on Likert scale responses.
- **Method:** Not an endpoint. This script is `require_once`'d and its function `calculateCompetencyScores()` is called directly by `php/save_data.php`.
- **Function:** `calculateCompetencyScores(array $responses)`
  - **Input:** Associative array `$responses` where keys are `question_id` (e.g., "q1_0") and values are the numeric responses (e.g., "4") for a single user and attempt.
  - **Process:**
    1.  Defines an array mapping competency category names to their corresponding question ID prefixes (e.g., `'Suchen...' => ['q1_']`).
    2.  Iterates through each category.
    3.  For each category, iterates through the input `$responses`.
    4.  If a `question_id` matches a prefix for the current category and the `response` is numeric, it adds the response value to the category's sum and increments the category's question count.
    5.  Calculates the average score for the category: `round((sum / count) / 6 * 100)`. (Assumes a 0-6 scale).
    6.  Calculates an overall score based on the average of _all_ valid numeric responses across categories.
  - **Output:** Returns an associative array `$scores` where keys are category names (including "overall") and values are the calculated scores (0-100).

### 6.6 Admin Endpoints (`dashboard-login.php`, `dashboard-data.php`)

#### `php/dashboard-login.php`

- **Purpose:** Authenticates an administrator for dashboard access.
- **Method:** `POST`
- **Request Body:** `application/json`
  - `userId` (string, required): Admin username.
  - `password` (string, required): Admin password.
- **Process:**
  1.  Decodes JSON body.
  2.  Connects to DB.
  3.  `SELECT`s `id`, `password` from `dashboard_users` where `user_id` matches.
  4.  If user found, compares the submitted `password` with the one stored in the database (**currently plain text comparison - NEEDS HASHING**).
  5.  If match, generates a simple token (e.g., `bin2hex(random_bytes(32))`).
  6.  Returns `{"token": "generated-token"}`.
  7.  If no match or user not found, returns 401 Unauthorized with `{"error": "Invalid credentials"}`.
- **Success Response:** `200 OK`, JSON `{"token": "..."}`
- **Error Response:** `401 Unauthorized`, JSON `{"error": "Invalid credentials"}`

#### `php/dashboard-data.php`

- **Purpose:** Retrieves and aggregates data for all users for the admin dashboard.
- **Method:** `GET`
- **Headers:**
  - `Authorization: Bearer [token]` (required): Token obtained from `dashboard-login.php`.
- **Process:**
  1.  Validates the Authorization token (currently accepts any non-empty token for simplicity - **NEEDS PROPER VALIDATION**).
  2.  Connects to DB.
  3.  Fetches all users from the `users` table.
  4.  Fetches all responses from `responses`, `pre_survey_responses`, `open_ended_responses`.
  5.  Fetches all scores from `scores`.
  6.  Fetches distinct question IDs from `responses`.
  7.  Aggregates data, structuring it into an array of user objects, where each object contains the user's details, timestamps, and nested objects/arrays for their responses and scores across all attempts (e.g., `initialResponses`, `updatedScores`).
  8.  Returns a JSON object: `{"users": [ /* array of user objects */ ], "questionIds": [ /* array of unique question IDs */ ]}`.
- **Success Response:** `200 OK`, JSON `{"users": [...], "questionIds": [...]}`
- **Error Response:** `500 Internal Server Error`, JSON `{"error": true, "message": "..."}` or `401 Unauthorized` if token validation fails (when implemented).

---

## 7. Frontend Structure

### 7.1 HTML Page Structure & Purpose

The website uses standard HTML5 files for each distinct view or page.

| Filename                | Purpose & Key Elements                                                                                                                                                                                                                                                                                                                                              |
| :---------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `index.html`            | Landing page. Features banner sections linking to Survey, Courses, Team. Contains standard header/footer.                                                                                                                                                                                                                                                           |
| `login.html`            | Entry point for users. Contains radio buttons for participation status, dynamic choice cards for actions (register, continue, redo, etc.), code input field.                                                                                                                                                                                                        |
| `generateCode.html`     | Contains the form (`<form id="generateCodeForm">`) with inputs for birthplace, parent name, birthday, school location used to create the user code.                                                                                                                                                                                                                 |
| `codeConfirmation.html` | Displays the generated code (`<span id="codeText">`) and provides a button (`#startSurveyButton`) to begin T1.                                                                                                                                                                                                                                                      |
| `survey.html`           | The main survey interface. Contains a container (`<form id="surveyForm">`) where `survey.min.js` dynamically injects HTML for the current section's questions. Also includes progress bar elements (`#progressBar`, `#progressText`) and header with user code/logout. The results section (`#results-section`) is also dynamically populated here upon completion. |
| `results.html`          | Dedicated page to display past results. Contains a canvas (`<canvas id="resultsChart">`) for the chart, a description box (`#resultsDescriptionBox`), ILIAS links, and PDF export button (`#exportPdfBtn`).                                                                                                                                                         |
| `project.html`          | Static content page describing the Open-Digi research project.                                                                                                                                                                                                                                                                                                      |
| `team.html`             | Static content page displaying project team members.                                                                                                                                                                                                                                                                                                                |
| `datenschutz.html`      | Static content page detailing the data protection policy.                                                                                                                                                                                                                                                                                                           |
| `courses.html`          | Static content page with interactive cards describing digital competency areas.                                                                                                                                                                                                                                                                                     |
| `dashboard.html`        | Admin dashboard interface. Contains controls (buttons, search, date picker), the main data table (`<table id="userTable">`), pagination area (`#pagination`), and visualization sidebar (`#visualizationSidebar`).                                                                                                                                                  |
| `dashboard-login.html`  | Simple page with a form (`<form id="loginForm">`) for admin username/password entry.                                                                                                                                                                                                                                                                                |

### 7.2 CSS Styling Strategy

- **Global Styles (`css/styles.css`):** Defines base element styles (body, headings, paragraphs), layout for header and footer, common button styles, and CSS variables (`:root`) for colors, fonts, etc.
- **Page-Specific Styles (`css/*.css`):** Each major page or feature (login, survey, dashboard, team, etc.) has its own CSS file to keep styles organized and avoid conflicts. These files style elements unique to that page.
- **CSS Variables:** Used extensively for theming (colors, fonts). Modifying variables in `styles.css` can quickly change the site's overall look.
- **Layout:** Primarily uses Flexbox and some Grid (e.g., `courses.html`, `dashboard.html` table structure) for layout.
- **Responsiveness:** Media queries (@media) are used within the CSS files (especially `styles.css` and page-specific ones) to adjust layout and styling for different screen sizes (desktops, tablets, mobiles).
- **Naming Convention:** Uses `kebab-case` for CSS class and ID selectors.

### 7.3 JavaScript Architecture & Key Files

The JavaScript is written in Vanilla JS (ES6+) and organized into files based on functionality. There is no module system or bundler used; files are included directly in HTML via `<script>` tags. Minified versions (`.min.js`) are typically used in production.

- **`script.min.js` (Core/Shared Logic):**
  - Handles initialization on page load (`DOMContentLoaded`).
  - Manages login form (`login.html`) interactions and submission logic (`handleLogin`).
  - Manages code generation form (`generateCode.html`) validation and submission (`handleCodeGenerationFormSubmission`).
  - Sets up event listeners for common elements like navigation buttons and logout.
  - Contains utility functions shared across pages (e.g., chart configuration helpers `createCompetencyChartConfig`, `updateDescriptionBox`, potentially `escapeHtml` if used globally).
  - Initializes PDF export functionality on `results.html`.
- **`survey.min.js` (Survey Engine):**
  - The most complex script, managing the entire survey lifecycle on `survey.html`.
  - `loadUserData()`: Fetches user progress/data from `php/get_user_data.php`.
  - `renderSection()`: Dynamically generates HTML for survey questions based on `survey-data.min.js`, `currentSection`, `attemptNumber`, and `group`. Handles conditional logic and populates existing answers. Locks fields for T2/T3.
  - `validateSection()` / `validateDatenschutz()`: Performs client-side validation.
  - `nextSection()` / `previousSection()`: Handles navigation, validation triggers, and saving progress via `saveSectionData()`.
  - `saveSectionData()`: Gathers form data and sends it to `php/save_data.php`.
  - `finishSurvey()`: Called after validating the last section; triggers final save (`isComplete=true`) and calls `showResults()`.
  - `showResults()`: Displays the completion message, chart (using `createCompetencyChart1`), and reflection prompts based on group/attempt. Handles submission of reflections via `php/save-open-ended-response.php`.
  - Manages the progress bar (`updateProgressBar`).
- **`survey-data.min.js` (Survey Content):**
  - Not executable code, but a crucial data structure (JavaScript array) defining all survey sections, questions, types, options, and requirements. **This is the primary file to edit when changing survey content.**
- **`dashboard.js` (Admin Dashboard):**
  - Handles all functionality on `dashboard.html`.
  - `fetchData()`: Gets all user data from `php/dashboard-data.php` (requires auth token).
  - `renderTable()`: Builds the HTML for the main data table based on fetched/filtered data.
  - `filterUsers()`: Filters data based on search and date range inputs.
  - `updatePagination()` / `changePage()`: Manages table pagination.
  - `exportSelectedData()`: Generates and downloads a CSV file for selected users.
  - `openVisualization()` / `closeVisualization()` / `updateVisualization()`: Manages the chart display sidebar.
  - Initializes Flatpickr date picker.
- **Other JS Files:**
  - `courses.js`: Handles clicks on competency cards on `courses.html`.
  - `dashboard-login.js`: Simple script to handle the admin login form submission.
  - `user-code-display.js`: Displays user code in the header.

### 7.4 Client-Side State Management (`sessionStorage`)

The application relies heavily on the browser's `sessionStorage` to maintain state _during a single user session_. This data is lost when the browser tab/window is closed.

- **Key `sessionStorage` Items:**

  - `userId`: The database ID of the logged-in user. **Essential** for associating saved data correctly. Set after successful login/registration.
  - `attemptNumber`: The current survey attempt (1, 2, or 3). Set after login.
  - `currentSection`: The index of the survey section the user is currently viewing. Updated by `survey.min.js` during navigation. Used by `loadUserData` to potentially resume.
  - `generatedCode` / `resultsCode`: The user's unique code string. Used for display and potentially for re-authentication if needed (though `userId` is primary).
  - `t3FirstStepCompleted_B` / `t3FirstStepCompleted_D`: Temporary flags used only during the T3 results display for specific groups to manage multi-step feedback.

- **Limitations:** Because `sessionStorage` is temporary, users cannot close their browser mid-survey and reliably resume later _unless_ the `currentSection` was successfully saved to the database via `php/save_data.php` before closing. Upon next login, `loadUserData` fetches the saved `currentSection` from the DB to resume.

---

## 8. Key Functionality Documentation

### 8.1 User Code Generation & Login Flow

1.  **Generation (`generateCode.html` -> `script.min.js` -> `php/register.php`):**
    - User inputs non-identifying info (initials, day).
    - `script.min.js` (`generateCodeFromForm`) concatenates inputs into XX-XX-XX-XX format.
    - `handleCodeGenerationFormSubmission` validates inputs client-side.
    - Sends the generated `code` via POST to `php/register.php`.
    - `register.php` checks DB for code uniqueness. If unique, `INSERT`s into `users` table and returns the new `userId`.
    - `script.min.js` stores `userId` and `generatedCode` in `sessionStorage`.
    - Redirects to `codeConfirmation.html`.
2.  **Confirmation & Start (`codeConfirmation.html` -> `script.min.js` -> `php/login.php`):**
    - Displays the `generatedCode` from `sessionStorage`.
    - User clicks "Mit der Befragung beginnen".
    - `setupStartSurveyButton` sends the `code` (and option="register") via POST to `php/login.php` to confirm the user exists and get attempt number (defaults to 1).
    - `login.php` confirms user, returns `userId` and `attemptNumber=1`.
    - `script.min.js` confirms `userId` and `attemptNumber` in `sessionStorage`.
    - Redirects to `survey.html`.
3.  **Login (`login.html` -> `script.min.js` -> `php/login.php`):**
    - User selects options (participation, action) and enters `code`.
    - `handleLogin` sends `code` and options (including `startNewAttempt`, `attemptType` if applicable) via POST to `php/login.php`.
    - `login.php` verifies code, finds `userId`, determines/updates `attempt_number` based on selected action, returns `userId` and `attemptNumber`.
    - `script.min.js` stores `userId` and `attemptNumber` in `sessionStorage`.
    - Redirects to `survey.html`.

### 8.2 Survey Engine (`survey.min.js` Deep Dive)

- **Initialization (`loadUserData`):**
  - Gets `userId` from `sessionStorage`. If missing, redirects to login.
  - Fetches all data for `userId` from `php/get_user_data.php`.
  - Populates the global `userData` object.
  - Determines the starting `currentSection` (usually `-2` for T1, `0` for T2/T3, or the saved value if resuming).
  - Calls `renderSection()` to display the initial section.
- **Rendering (`renderSection`):**
  - Clears the `#surveyForm` container.
  - Selects the correct section data from `surveyData` based on `currentSection`.
  - Handles special rendering for Pre-Survey (`-2`) and Datenschutz (`-1`).
  - For main sections (`>=0`):
    - Iterates through `questions` array for the section.
    - Checks conditional logic (`dependsOn`, `showOnlyInT2`, `showOnlyForGroups`).
    - Generates HTML for each visible question based on `type`.
    - Populates values from the correct attempt in `userData` (`initialResponses`, `updatedResponses`, `followUpResponses`).
    - Applies `required`, `readonly`, `disabled` attributes.
    - Adds specific inputs like T2 course feedback if applicable.
  - Injects the generated HTML into `#surveyForm`.
  - Calls `updateNavigationButtons()`.
- **Navigation (`nextSection`, `previousSection`):**
  - `nextSection`: Validates current section -> Saves progress (`saveSectionData(false)`) -> Increments `currentSection` -> Renders next section. Handles transitions between special/main sections and triggers `finishSurvey` on last section.
  - `previousSection`: Decrements `currentSection` -> Renders previous section.
- **Validation (`validateSection`, `validateDatenschutz`):**
  - Uses HTML5 `checkValidity()` on visible, required fields.
  - Adds/removes `.unanswered` class and visual cues (borders, shake animation) for user feedback.
- **Saving (`saveSectionData`):**
  - Gathers all data from the current form using `FormData`.
  - Appends `userId`, `attemptNumber`, `currentSection`, `isComplete`.
  - Appends special data (`datenschutzConsent`, `preSurveyResponses`, open-ended fields) if provided/present.
  - Sends data via POST to `php/save_data.php`.
  - Returns the parsed JSON response from PHP.
- **Completion (`finishSurvey`, `showResults`):**
  - `finishSurvey`: Called by `nextSection` after validating the _last_ survey section. Calls `saveSectionData(true)`.
  - `showResults`: Called after successful final save.
    - Fetches the _latest_ user data (including calculated scores) via `php/get_user_data.php`.
    - Hides survey form/progress bar.
    - Dynamically generates HTML for the results display within `#surveyForm` (or `#results-section`).
    - **Crucially, uses `group` and `attemptNumber` to determine:**
      - Introductory text.
      - Whether to show the chart (`createCompetencyChart1`).
      - Which reflection prompt to display (`t1_strategy`, `t2_reflection`, `t3_reflection`, `t3_additional_reflection`).
      - Whether multi-step T3 feedback is needed (Groups B/D).
      - Final messages and links.
    - Sets up event listeners for reflection submission buttons.

### 8.3 Group-Specific Logic (A, B, C, D)

The primary differences between groups manifest in the `showResults()` function in `survey.min.js`:

- **Group A (Control/Standard):**
  - T1: Shows chart, prompts for T1 strategy (course selection rationale), shows ILIAS links after submission.
  - T2: Shows comparison chart (T1 vs T2), prompts for T2 reflection.
  - T3: Shows comparison chart (T1 vs T2 vs T3), prompts for T3 reflection.
- **Group B (Self-Selection Focus):**
  - T1: Shows competency descriptions _instead_ of chart, prompts for T1 strategy (course selection rationale), shows ILIAS links after submission.
  - T2: Shows _no chart_, prompts for T2 reflection.
  - T3: **Multi-step:**
    1.  Prompts for T3 reflection (no chart).
    2.  After submission, sets `t3FirstStepCompleted_B` flag, re-renders to show comparison chart (T1/T2/T3) and prompts for _additional_ T3 reflection comparing initial thoughts to the chart.
- **Group C (Score Focus):**
  - T1: Shows chart, displays overall score prominently, provides brief text, no reflection prompt initially.
  - T2: Shows comparison chart (T1 vs T2), displays overall score, prompts for T2 reflection.
  - T3: Shows comparison chart (T1 vs T2 vs T3), displays overall score, prompts for T3 reflection.
- **Group D (Delayed Feedback):**
  - T1: Shows _no chart or score_, simple thank you message.
  - T2: Shows _no chart_, prompts for T2 reflection.
  - T3: **Multi-step:**
    1.  Prompts for T3 reflection (no chart).
    2.  After submission, sets `t3FirstStepCompleted_D` flag, re-renders to show comparison chart (T1/T2/T3) and prompts for _additional_ T3 reflection comparing initial thoughts to the chart.

The group is determined from `userData.preSurveyResponses['q-2_1']` (fetched from the DB).

### 8.4 Score Calculation & Display

1.  **Trigger:** Score calculation happens server-side (`php/calculate-scores.php`) only when `php/save_data.php` is called with `isComplete=true`.
2.  **Process (`calculateCompetencyScores` function):**
    - Receives all responses for the completed attempt.
    - Groups questions by competency category based on prefixes (q1\_, q2\_, etc.).
    - Calculates the average Likert score (0-6) for each category.
    - Converts the average to a percentage score (0-100) using `round((avg / 6) * 100)`.
    - Calculates an overall percentage score.
    - Saves scores to the `scores` table.
3.  **Display:**
    - `showResults()` (in `survey.min.js`) or JS on `results.html` fetches scores via `php/get_user_data.php`.
    - Scores for T1, T2, T3 are extracted from the `initialScores`, `updatedScores`, `followUpScores` objects.
    - Data is formatted and passed to `createCompetencyChartConfig` (in `script.min.js`) which configures Chart.js to render the bar chart comparing attempts.
    - The description box below the chart updates on hover to show category details.

### 8.5 Results Page & PDF Export

- **`results.html`:** A dedicated page for users to view their results _after_ logging in via the "View Results" option on `login.html`.
- **Loading:** On page load, JavaScript fetches user data using the `userId` from `sessionStorage` via `php/get_user_data.php`.
- **Display:** It uses the same `createCompetencyChartConfig` helper to render the results chart (`#resultsChart`) similar to the survey completion page. It also displays the T1 strategy text (`#t1StrategyText`) if available and provides static links to ILIAS courses.
- **PDF Export (`#exportPdfBtn`):**
  - Uses the `html2canvas` library to capture the `.results-container` div (or a specific part of it) as a canvas image.
  - Uses the `jsPDF` library to create a new PDF document.
  - Adds the captured canvas image to the PDF, scaling it to fit the page.
  - Triggers a browser download of the generated PDF file named `OpenDigi_Ergebnisse.pdf`.

### 8.6 Admin Dashboard Functionality

- **Authentication:** Login via `dashboard-login.html` uses `php/dashboard-login.php` to verify credentials against `dashboard_users`. A simple token is stored in `localStorage` upon success.
- **Data Fetching (`dashboard.js` -> `php/dashboard-data.php`):** `fetchData` sends the token in the Authorization header. `dashboard-data.php` verifies the token (currently basic check) and fetches/aggregates data for _all_ users.
- **Table Rendering (`renderTable`):** Dynamically builds the large HTML table row by row, including user info, timestamps, pre-survey/open-ended answers, and individual responses for T1/T2/T3 for _all_ survey questions (`qX_Y`). Uses `escapeHtml` for safety.
- **Filtering (`filterUsers`):** Filters the displayed rows based on text search (`#userSearch`) across multiple fields and date range (`#dateRange`) applied to T1/T2/T3 timestamps.
- **Pagination (`updatePagination`, `changePage`):** Limits the number of rows displayed per page (`usersPerPage`) and provides Prev/Next controls.
- **Export (`exportSelectedData`):** Generates a CSV file containing data for users selected via checkboxes. Constructs headers and rows, escapes data using `escapeCsvValue`, and triggers download.
- **Visualization (`updateVisualization`):** When checkboxes are selected, displays a competency chart for the _last selected_ user in the sidebar using `createCompetencyChartConfig`. Allows downloading this chart image.

---

## 9. Code Style Guide

_(This section outlines observed styles and recommendations)_

### 9.1 Naming Conventions

- **HTML:**
  - IDs: `camelCase` (e.g., `userCodeDisplay`, `progressBar`).
  - Classes: `kebab-case` (e.g., `survey-header`, `navigation-buttons`).
  - Data Attributes: `data-kebab-case` (e.g., `data-condition`).
- **JavaScript:**
  - Variables/Functions: `camelCase` (e.g., `userData`, `renderSection`).
  - Constants: `camelCase` or `UPPER_SNAKE_CASE` (e.g., `colorMap`, `USERS_PER_PAGE`).
- **PHP:**
  - Variables: `camelCase` (e.g., `$userId`, `$conn`).
  - Functions: `camelCase` (e.g., `calculateCompetencyScores`).
  - Database Tables/Columns: `snake_case` (e.g., `user_code`, `attempt_number`). **Maintain consistency with the database schema.**
- **CSS:**
  - Selectors (Classes/IDs): `kebab-case` (e.g., `.team-member`, `#resultsChart`).
  - Variables: `--kebab-case` (e.g., `--primary-color`).

### 9.2 Formatting Practices

- **Indentation:** Use 2 spaces for HTML, CSS, JS. Use 4 spaces for PHP (following PSR standards).
- **Quotes:** Prefer single quotes (`'`) in JS, unless double quotes (`"`) are needed. Prefer single quotes in PHP unless interpolation is required. Use double quotes for HTML attributes.
- **Semicolons:** Always use semicolons at the end of JS and PHP statements.
- **Braces:**
  - JS/CSS: Opening brace on the same line (`if (condition) {`, `.class {`).
  - PHP: Opening brace on the same line for control structures (`if`, `foreach`), next line for functions/classes.
- **Spacing:** Use spaces around operators (`=`, `+`, `-`, `==`, etc.) and after commas.
- **Line Length:** Aim for lines under 100-120 characters for readability, though not strictly enforced.

### 9.3 Commenting Standards

- **File Headers:** Add a block comment at the top of `.js` and `.php` files explaining their overall purpose.
  ```php
  <?php
  /**
   * db_connect.php
   * Establishes the connection to the MariaDB/MySQL database.
   * Contains critical configuration variables.
   */
   // ...
  ```
- **Function Headers (JSDoc/PHPDoc):** Use documentation blocks for non-trivial functions.
  ```javascript
  /**
   * Fetches all relevant data for a specific user.
   * @param {number} userId - The database ID of the user.
   * @returns {Promise<object|null>} A promise that resolves with the user data object, or null on error.
   */
  async function loadUserData(userId) {
    // ...
  }
  ```
  ```php
  /**
   * Calculates competency scores based on responses.
   *
   * @param array $responses Associative array of question_id => response.
   * @return array Associative array of category => score.
   */
  function calculateCompetencyScores(array $responses): array {
      // ...
  }
  ```
- **Inline Comments:** Use `//` (JS/PHP) or `/* */` to explain complex algorithms, workarounds, or non-obvious logic sections. Avoid commenting obvious code.
- **TODO/FIXME:** Use `// TODO:` for planned improvements or `// FIXME:` for known issues needing fixes.

### 9.4 Project-Specific Patterns

- **Survey Question IDs:** Follow `q{sectionIndex}_{questionIndex}` or `q-2_{index}` format.
- **Open-Ended Keys:** Use descriptive keys like `t1_strategy`, `t2_reflection`.
- **Group Logic:** Explicitly check `group` variable (`'Gruppe A'`, `'Gruppe B'`, etc.) in conditional statements.
- **Error Handling:** PHP scripts generally return JSON `{"ok": false, "message": "..."}` or `{"success": false, ...}`. JS uses `try...catch` with `fetch` and displays errors using `SweetAlert2`.
- **DOM Manipulation:** Primarily uses standard `document.getElementById`, `document.querySelector`, `element.innerHTML`, `element.appendChild`, etc.

---

## 10. Maintenance and Deployment Guide

_(Sections 10.1 to 10.4 remain largely the same as the previous detailed version, focusing on adding questions, modifying logic, database updates, deployment checklist, and backup procedures. Ensure the backup commands and paths are correctly specified.)_

_(Include the detailed steps for Adding Questions, Modifying Logic, DB Updates, Deployment Checklist, and Backup Procedures from the previous comprehensive answer here)_

---

## 11. Common Issues and Troubleshooting

### 11.1 Debugging Tools & Techniques

- **Browser Developer Tools (F12):**
  - **Console:** Essential for viewing JavaScript errors, `console.log()` output, and network request failures.
  - **Network:** Inspect HTTP requests made by `fetch`. Check request headers, parameters, status codes (200 OK, 404 Not Found, 500 Server Error), and the "Response" tab for JSON data or PHP error messages.
  - **Elements:** Inspect the HTML structure and applied CSS styles. Check if elements exist and have the expected attributes/content.
  - **Application/Storage:** Inspect `sessionStorage` to see current values for `userId`, `attemptNumber`, `currentSection`, etc. Inspect `localStorage` for the `dashboardToken`.
- **PHP Error Logs:**
  - **Location:** Varies by server setup. Common locations: `/var/log/apache2/error.log`, `/var/log/nginx/error.log`, `/var/log/php/error.log`, or within XAMPP/MAMP log directories. Check your `php.ini` (`error_log` directive).
  - **Usage:** Check these logs for detailed PHP errors (syntax errors, database connection failures, undefined variables, exceptions) that don't appear in the browser.
  - **Debugging Output:** Use `error_log("Debug: User ID = " . $userId);` within PHP scripts to trace execution flow and variable values without breaking JSON output to the browser. **Remove debug logs before production.**
- **Database Client (phpMyAdmin, etc.):**
  - Directly inspect table structures and data.
  - Manually run SQL queries to test them before putting them in PHP.
  - Check user privileges if connection fails.

### 11.2 Specific Problem Scenarios & Solutions

- **User Cannot Log In ("Invalid Code"):**
  - _Check:_ User input format (XX-XX-XX-XX, uppercase).
  - _Check:_ `users` table for the exact `user_code`.
  - _Debug:_ Add `error_log()` in `php/login.php` to see the received `$code` and the result of the `SELECT` query. Check DB connection.
- **Survey Stuck / Cannot Proceed:**
  - _Check:_ Browser Console for JS errors (often in `survey.min.js` `nextSection` or `validateSection`).
  - _Check:_ Are all _visible_ required fields filled? Use Elements tab to inspect fields.
  - _Check:_ Network tab for failed requests to `php/save_data.php` (Status 500?). Look at the Response tab for PHP errors.
  - _Debug:_ Add `console.log()` in `nextSection` to trace progress. Check PHP error logs for `save_data.php` issues.
- **Results Chart Not Displaying:**
  - _Check:_ Browser Console for Chart.js errors or errors in `showResults`/`createCompetencyChart1`.
  - _Check:_ Network tab for the request to `php/get_user_data.php`. Was it successful? Did it return score data (`initialScores`, etc.)?
  - _Debug:_ `console.log()` the score data _before_ passing it to chart functions in `showResults`. Check `scores` table in DB.
- **Admin Dashboard Blank / Data Missing:**
  - _Check:_ Is the user logged in? Check `localStorage` for `dashboardToken`.
  - _Check:_ Network tab for the request to `php/dashboard-data.php`. Was it successful (Status 200)? Was the `Authorization: Bearer ...` header sent? Did the response contain valid JSON with a `users` array?
  - _Debug:_ Check PHP error logs for `dashboard-data.php`. Add `error_log()` to trace its execution and database queries. `console.log()` the received data in `dashboard.js` `fetchData`.
- **PDF Export Fails:**
  - _Check:_ Browser Console for `html2canvas` or `jspdf` errors.
  - _Check:_ Network tab to ensure the libraries loaded correctly from the CDN.
- **Group Logic Incorrect:**
  - _Verify:_ How is the `group` variable determined in `survey.min.js` (`loadUserData` or `showResults`)? Ensure it correctly reads from `userData.preSurveyResponses['q-2_1']`.
  - _Debug:_ `console.log("Current Group:", group, "Attempt:", attemptNumber);` at the start of `showResults` and within conditional blocks.

---

## 12. Security Considerations

### 12.1 Current Measures & Identified Vulnerabilities

- **Measures:**
  - Client-side form validation (basic).
  - Use of `mysqli` prepared statements in most DB interactions (good SQLi prevention).
  - Admin dashboard has basic login (but flawed).
- **Vulnerabilities:**
  - **Plain Text Admin Password (High Risk):** `dashboard_users.password` is not hashed.
  - **Insufficient Server-Side Validation (Medium Risk):** PHP scripts may not rigorously check input types, lengths, or formats before database operations.
  - **Potential XSS (Medium Risk):** User-provided data (especially open-ended responses, potentially user code if displayed incorrectly) might not be consistently escaped with `htmlspecialchars()` before being outputted to HTML (dashboard, potentially results).
  - **No CSRF Protection (Medium Risk):** Forms lack CSRF tokens, making state-changing actions potentially vulnerable.
  - **Session Management Weakness (Low-Medium Risk):** Over-reliance on `sessionStorage` could be less secure than server-side sessions, though impact is limited given the application's nature.
  - **Error Disclosure (Potential Risk):** If `display_errors` is On in production PHP, sensitive information could be leaked.

### 12.2 Recommended Security Hardening Steps

1.  **(HIGH PRIORITY) Implement Password Hashing:**
    - Modify `php/dashboard-login.php` to use `password_verify()` for checking passwords.
    - Create a one-time script or manually update passwords in the `dashboard_users` table using `password_hash($plainPassword, PASSWORD_DEFAULT)`.
2.  **Enforce Strict Server-Side Validation:**
    - In all PHP endpoints receiving data (`save_data.php`, `register.php`, `login.php`, etc.):
      - Explicitly check data types (e.g., `is_numeric()`, `filter_var($userId, FILTER_VALIDATE_INT)`).
      - Validate string lengths (`strlen()`).
      - Validate formats where applicable (e.g., user code format).
      - Use `htmlspecialchars()` when preparing data _for database insertion_ if it might be displayed later, or **definitely** when echoing data back into HTML.
3.  **Implement CSRF Protection:**
    - Start PHP sessions (`session_start()`).
    - On pages with forms: Generate a unique token, store it in `$_SESSION['csrf_token']`, and include it as `<input type="hidden" name="csrf_token" value="...">` in the form.
    - In the PHP script processing the form: Check if `$_POST['csrf_token']` exists and matches `$_SESSION['csrf_token']`. Reject the request if it doesn't match or is missing. Regenerate the token after successful processing.
4.  **Consistent XSS Prevention:**
    - Review all PHP files where data is outputted to HTML (especially `dashboard.js` rendering and potentially `survey.min.js` if it ever displays raw user input).
    - Wrap **all** echoed variables originating from the database or user input with `htmlspecialchars($variable, ENT_QUOTES, 'UTF-8')`.
5.  **Configure PHP for Production:**
    - Ensure `display_errors = Off` and `log_errors = On` in the production `php.ini`.
    - Set a secure `error_log` path.
6.  **Review File Permissions:** Ensure restrictive permissions (e.g., 644 for files, 755 for directories) on the server.
7.  **HTTPS:** Ensure the live site forces HTTPS connections (usually configured at the web server level - Apache/Nginx).

---

## 13. Future Enhancement Opportunities

### 13.1 Technical Debt & Refactoring Ideas

- **Refactor `survey.min.js`:** Break down the large `renderSection` and `showResults` functions into smaller, more manageable helper functions. Improve separation of concerns (DOM manipulation vs. state logic vs. API calls).
- **Refactor `script.min.js`:** Separate logic for different pages (`login`, `generateCode`, `results`) into distinct modules or files if a build process is introduced. Extract utility functions.
- **Introduce Build Process:** Use Node.js/npm with tools like Vite or Parcel to manage JS dependencies, enable modern JS features (modules, newer syntax), bundle/minify code automatically, and manage CSS preprocessing if desired. This would significantly improve maintainability over manual minification.
- **PHP Structure:** Organize PHP endpoints using a simple router or a micro-framework for cleaner request handling. Consider using classes for database interactions (e.g., a `UserRepository` class).
- **Replace `sessionStorage`:** Migrate essential state (`userId`, `attemptNumber`) to server-side PHP sessions for better persistence and security.
- **Address Security Issues:** Prioritize implementing password hashing and server-side validation/XSS prevention.

### 13.2 Potential New Features

- **Enhanced Admin Dashboard:** More sophisticated filtering/sorting, data visualizations (beyond single user charts), question-level analysis, user management capabilities.
- **Survey Editor:** An admin interface to add/edit/reorder survey questions directly, modifying `survey-data.min.js` via a UI instead of manual code editing.
- **Improved User Feedback:** Provide more detailed textual feedback on the results page based on score levels or patterns, potentially tailored by group.
- **"Resume Survey" Link:** Email users a unique link to resume their survey if they stop mid-way (would require storing email or another identifier and implementing PHP sessions).
- **Accessibility Improvements:** Conduct a formal accessibility audit (WCAG) and implement necessary fixes for screen reader users and keyboard navigation.
- **Internationalization (i18n):** Abstract all user-facing text strings into language files/databases to allow for translation into other languages.
- **Direct ILIAS Integration:** Use ILIAS APIs (if available) to automatically enroll users in recommended courses based on T1 results or pass completion status back.

---

## 14. Glossary of Terms

_(This section remains the same as the previous detailed version)_

| Term                     | Definition                                                                                                   |
| :----------------------- | :----------------------------------------------------------------------------------------------------------- |
| **T1 / T2 / T3**         | Time points for the survey: T1=Initial, T2=Post-Intervention, T3=Follow-up (approx. 1 month after T2).       |
| **Attempt (Number)**     | Refers to which survey iteration (1, 2, or 3) the data belongs to.                                           |
| **User Code**            | The unique XX-XX-XX-XX code generated by/for a user for anonymous identification.                            |
| **Group (A, B, C, D)**   | Experimental groups determining the user's experience (e.g., feedback type, reflection prompts).             |
| **Competency Category**  | One of the six main digital competency areas assessed (e.g., "Suchen...", "Kommunikation...").               |
| **Score**                | A calculated value (0-100) representing perceived competence in a category, based on Likert responses.       |
| **Open-Ended Response**  | Free-text feedback/reflection provided by the user at specific points (e.g., T1 strategy, T2/T3 reflection). |
| **Session Storage**      | Browser storage used to hold temporary user state (`userId`, `attemptNumber`) during a single session.       |
| **CDN**                  | Content Delivery Network. Used to host external libraries like Chart.js, SweetAlert2.                        |
| **ERD**                  | Entity Relationship Diagram. A visual representation of database tables and their relationships.             |
| **Vanilla JS/PHP**       | Plain JavaScript or PHP code written without relying on large frameworks like React, Laravel, etc.           |
| **Prepared Statements**  | A security feature in database interactions (used via `mysqli::prepare`) to prevent SQL injection.           |
| **XAMPP/MAMP/WAMP/LAMP** | Software stacks providing Apache/Nginx, MySQL/MariaDB, and PHP for local web development.                    |

---

## 15. Technical Debt Tracking

_(This section remains the same as the previous detailed version, listing known issues and priorities)_

| Issue                            | Priority | Description                                                                      | Potential Fix                                                           | Status   |
| :------------------------------- | :------- | :------------------------------------------------------------------------------- | :---------------------------------------------------------------------- | :------- |
| Admin password storage           | **High** | Passwords stored in plain text in `dashboard_users`.                             | Implement `password_hash()` / `password_verify()`.                      | **Open** |
| Lack of Server-Side Validation   | Medium   | Relies heavily on client-side validation; potential for invalid data submission. | Add explicit type/length/format checks in PHP before DB operations.     | **Open** |
| Potential XSS vulnerabilities    | Medium   | User input might be echoed without consistent `htmlspecialchars()`.              | Review all PHP output points, apply `htmlspecialchars()` consistently.  | **Open** |
| No CSRF Protection               | Medium   | Forms vulnerable to Cross-Site Request Forgery.                                  | Implement CSRF token generation and validation.                         | **Open** |
| Reliance on `sessionStorage`     | Medium   | State lost on browser close; less secure than PHP sessions for critical data.    | Migrate `userId`, `attemptNumber` to PHP sessions.                      | **Open** |
| Minified JS Code Readability     | Low      | `script.min.js`, `survey.min.js` are hard to debug/maintain directly.            | Set up a build process to generate minified files from source versions. | **Open** |
| Large JS Files (`survey.min.js`) | Low      | Can become hard to manage as features grow.                                      | Refactor into smaller modules/functions.                                | **Open** |
| Inconsistent PHP Error Handling  | Low      | Some scripts might lack robust `try/catch` blocks or error logging.              | Standardize error handling, use `error_log()`.                          | **Open** |

---

## 16. File Structure Map

_(This section remains the same as the previous detailed version, providing a tree view of the project folders and key files)_

```

open-digi-survey-website/
├── css/ # CSS stylesheets
│ ├── courses.css # Styles for competency areas page
│ ├── dashboard.css # Admin dashboard styles
│ ├── datenschutz.css # Privacy policy page styles
│ ├── index.css # Landing page styles
│ ├── login.css # Login/registration styles
│ ├── project.css # Project information page styles
│ ├── styles.css # Global styles (header, footer, base)
│ ├── survey.css # Survey form/results styles
│ └── team.css # Team page styles
├── js/ # JavaScript files
│ ├── courses.js # Competency areas page logic
│ ├── dashboard-login.js # Admin login handling
│ ├── dashboard.js # Admin dashboard functionality
│ ├── script.min.js # Core functionality (login, code gen, utils)
│ ├── survey-data.min.js # Survey questions data structure **_EDIT ME TO CHANGE QUESTIONS_**
│ ├── survey.min.js # Survey form handling & logic
│ └── user-code-display.js # User code display in header
├── php/ # Backend PHP scripts
│ ├── calculate-scores.php # Competency score calculation logic
│ ├── check-results.php # Results availability checking (for login page)
│ ├── dashboard-data.php # Admin dashboard data retrieval API
│ ├── dashboard-login.php # Admin login processing API
│ ├── db_connect.php # Database connection configuration **_EDIT ME FOR DEPLOYMENT_**
│ ├── get_user_data.php # User data retrieval API (for survey/results)
│ ├── login.php # User login processing API
│ ├── register.php # User registration API
│ ├── save-open-ended-response.php # Open-ended response saving API
│ └── save_data.php # Survey data saving API
├── images/ # Image assets
│ └── ... # Various image files (logo.png, icons, etc.)
├── codeConfirmation.html # Code confirmation page
├── courses.html # Competency areas information page
├── dashboard-login.html # Admin login page
├── dashboard.html # Admin dashboard page
├── datenschutz.html # Privacy policy page
├── generateCode.html # Code generation form page
├── index.html # Landing page
├── login.html # User login/options page
├── project.html # Project information page
├── results.html # Results display page
├── survey.html # Main survey form page
├── team.html # Team information page
├── DEVELOPER_DOCS.md # This documentation file
├── README.md # General project overview (often less detailed than this)
└── open_digi_db.sql # Database schema import file (ensure this exists)

```

---

## 17. Implementation Guide: Adding a New Survey Question

_(This section remains the same as the previous detailed version, providing a clear walkthrough for adding a question)_

This walkthrough demonstrates how to add a new Likert scale question to an existing survey section.

**Scenario:** Add a question "Ich kann effektiv Online-Meetings moderieren." to Section 2 ("Kommunikation und Kollaborieren").

### Step 1: Add the Question to the Data Structure

1.  Open `js/survey-data.min.js`.
2.  Locate the section object with `title: 'Kommunikation und Kollaborieren'`.
3.  Find the `questions` array within that section.
4.  Add a new question object to the end of the `questions` array:

    ```javascript
    // Inside the 'Kommunikation und Kollaborieren' section's questions array:
    // ... (previous questions) ...
    {
      text: 'Ich kann effektiv Online-Meetings moderieren.', // New question text
      type: 'scale', // Type is Likert scale
      required: true, // Make it mandatory
    } // Add comma if not the absolute last item in the array
    ```

5.  Save the `js/survey-data.min.js` file.

### Step 2: Test the Question Display and Validation

1.  Clear your browser's cache or use an incognito window to ensure the updated `survey-data.min.js` is loaded.
2.  Generate a new user code or log in as a test user.
3.  Navigate through the survey until you reach the "Kommunikation und Kollaborieren" section.
4.  **Verify:** The new question "Ich kann effektiv Online-Meetings moderieren." should appear at the end of the section with the 0-6 scale buttons.
5.  **Test Validation:** Try clicking "Next" without selecting a value for the new question. The section validation should fail, and the question should be highlighted as unanswered. Select a value and ensure you can proceed.

### Step 3: Verify Data Storage

1.  Complete the survey section containing the new question (and potentially finish the attempt).
2.  Access the database (e.g., via phpMyAdmin).
3.  Find the `user_id` for your test user in the `users` table.
4.  Determine the `question_id` for the new question. Since it was added to the end of Section 2 (index 1), and assuming there were 7 questions before it (q1_0 to q1_6), the new ID would likely be `q1_7`. **Verify this by counting questions in `survey-data.min.js` for that section.**
5.  Check the `responses` table for a row matching your `user_id`, the correct `question_id` (e.g., 'q1_7'), and the current `attempt` number. Ensure the `response` column contains the value you selected on the scale.

    ```sql
    -- Replace [your_test_user_id] and [current_attempt_number]
    SELECT * FROM responses
    WHERE user_id = [your_test_user_id]
      AND question_id = 'q1_7' -- Adjust if needed based on actual position
      AND attempt = [current_attempt_number];
    ```

### Step 4: Verify Score Calculation

1.  Ensure the survey attempt is marked as complete (either finish it or manually set `is_complete=1` and run score calculation if needed for testing).
2.  The new question `q1_7` starts with "q1\_", which matches the prefix for the "Kommunikation und Kollaborieren" category defined in `php/calculate-scores.php`. Therefore, **no changes are needed** in `calculate-scores.php` for this specific question. Its numeric response will automatically be included in that category's score calculation.
3.  Check the `scores` table for the relevant `user_id` and `attempt_number`. The score for the 'Kommunikation und Kollaborieren' category should now reflect the inclusion of the new question's response in its average.

    ```sql
    -- Replace [your_test_user_id] and [current_attempt_number]
    -- Check exact category name in calculate-scores.php if different
    SELECT score FROM scores
    WHERE user_id = [your_test_user_id]
      AND category = 'Kommunikation und Kollaborieren'
      AND attempt_number = [current_attempt_number];
    ```

---

_This codebook provides a comprehensive guide to the Open-Digi Survey Website. Future developers should use this document as a starting point. For details not covered here, refer to source code comments or contact the project team._
