/* styles.css */

/* Root Variables for Consistency */
:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --font-family: 'Roboto', sans-serif;
  --text-color: #333;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: var(--font-family);
  color: var(--text-color);
  background: var(--background-color);
  line-height: 1.6;
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Styles */
header {
  background: var(--background-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  position: fixed; /* Fixed positioning */
  width: 100%;
  top: 0;
  z-index: 1000; /* Ensure header is above other content */
  border-bottom: 2px solid var(--primary-color);
}

header .logo {
  height: 50px;
  width: auto;
}

nav {
  display: flex;
  gap: 20px;
  align-items: center;
}

nav a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  padding: 10px;
  transition: color 0.3s;
}

nav a:hover {
  color: var(--link-hover-color);
}

nav a.active {
  border-bottom: 2px solid var(--primary-color);
}

/* Focus Styles for Accessibility */
nav a:focus,
button:focus,
.get-started-button:focus,
.section-button:focus {
  outline: 2px dashed var(--primary-color);
  outline-offset: 4px;
}

/* Main Content Area */
main {
  flex: 1;
  padding: 100px 20px 60px; /* Top padding accounts for fixed header */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%; /* Ensure main takes full width */
}

/* Section Spacing */
section {
  margin-bottom: 60px;
}

/* Typography */
h1 {
  font-size: 2.5em;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-weight: 700;
}

h2 {
  font-size: 2em;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-weight: 600;
}

h3 {
  font-size: 1.75em;
  margin-bottom: 15px;
  color: var(--primary-color);
  font-weight: 500;
}

p {
  font-size: 1em;
  margin-bottom: 20px;
  color: var(--text-color);
  line-height: 1.8;
}

li {
  font-size: 1em;
  margin-bottom: 10px;
  color: var(--text-color);
  line-height: 1.6;
}

/* Footer Styles */
footer {
  background: var(--primary-color);
  color: white;
  width: 100%;
  margin-top: auto; /* Push footer to the bottom */
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
}

footer p {
  color: white; /* Ensure text is white */
  margin: 0; /* Remove default margin */
  text-align: center;
}

/* Button Styles */
button,
.button,
.section-button,
.get-started-button {
  background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  padding: 12px 28px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 50px; /* Rounded buttons */
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0,74,153,0.2);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

button:hover,
.button:hover,
.section-button:hover,
.get-started-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0,74,153,0.3);
}

button:active,
.button:active,
.section-button:active,
.get-started-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(0,74,153,0.2);
}

/* Add icon support to buttons */
button i, .button i, .section-button i {
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  nav {
    flex-direction: column;
    align-items: center;
  }

  .get-started-button {
    margin-top: 10px;
  }
  /* Reduce padding on smaller screens */
  main {
    padding: 80px 10px 40px;
  }
}

/* Card Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.card-header {
  padding: 15px 20px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: white;
}

.card-body {
  padding: 20px;
}
