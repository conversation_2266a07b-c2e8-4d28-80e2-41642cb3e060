/* courses.css */

/* Root Variables */
:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --text-color: #333;
  --font-family: 'Roboto', sans-serif;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
  --card-shadow: 0 4px 6px rgba(0, 74, 153, 0.1);
  --card-hover-shadow: 0 8px 15px rgba(0, 74, 153, 0.2);
  --card-bg-color: #ffffff;
  --primary-color-rgb: 0, 74, 153; /* For rgba() use */
  --line-color: var(--primary-color); /* Easier to change line color */
  --line-thickness: 2px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: var(--font-family);
  color: var(--text-color);
  background: var(--background-color);
}

html {
  scroll-behavior: smooth;
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Introduction Section */
.introduction-section {
  max-width: 900px;
  margin: 120px auto 3.5rem;
  padding: 2.5rem;
  background-color: var(--light-background);
  border-radius: 15px;
  box-shadow: var(--card-shadow);
  text-align: center;
}

.introduction-section h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.introduction-section p {
  line-height: 1.6;
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Main Title */
.competency-title-container {
  text-align: center;
  margin: 40px auto 20px;
  position: relative;
}

.main-title {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: #ffffff;
  padding: 20px 40px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.25);
  display: inline-block;
  font-weight: 600;
  font-size: 2rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* Section Titles */
.section-titles {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto 2rem;
  padding: 0 2rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.section-titles h2 {
  background-color: var(--light-background);
  color: var(--primary-color);
  padding: 15px 30px;
  border: 2px solid var(--primary-color);
  border-radius: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.1);
  min-width: 300px;
  text-align: center;
  transition: all 0.3s ease;
}

.section-titles h2:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(var(--primary-color-rgb), 0.15);
  background: linear-gradient(145deg, var(--light-background), #ffffff);
}

/* Connection lines */
/* Vertical line from main title to horizontal line */
.section-titles::before {
  content: '';
  position: absolute;
  top: -42px;
  left: 48%;
  width: var(--line-thickness);
  height: 76px;
  background-color: var(--line-color);
  z-index: -2;
}

/* Horizontal line connecting section titles */
.section-titles::after {
  content: '';
  position: absolute;
  top: 40%;
  left: 38%;
  width: 180px;
  height: var(--line-thickness);
  background-color: var(--line-color);
  z-index: -1;
}

/* Competencies Container */
.competencies-container {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 60px auto 0;
  padding: 0 2rem;
  align-items: start;
  position: relative;
}
/* Competency Sections */
.competency-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  justify-items: center;
  position: relative;
  z-index: 2;
}

.competency-section:first-child {
  margin-top: 0;
}

/* Description Column */
.description-column {
  position: sticky;
  top: 5rem;
  width: 100%;
  max-width: 550px;
  margin: 1rem auto;
  padding: 1.5rem;
  background: linear-gradient(145deg, #ffffff, var(--card-bg-color));
  overflow-y: auto;
  max-height: calc(100vh - 10rem);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  grid-column: 2;
  grid-row: 1;
  z-index: 3;
  transition: all 0.3s ease;
}

.description-column:hover {
  box-shadow: var(--card-hover-shadow);
  transform: translateY(-2px);
}

.description-column h2 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.description-content {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 1.5rem;
  line-height: 1.6;
}

.description-content h3 {
  color: var(--secondary-color);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);
}

.description-content p {
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.description-content h3:first-child {
  margin-top: 0;
}

/* Competency Card */
.competency-card {
  width: 100%;
  background: linear-gradient(145deg, #ffffff, var(--card-bg-color));
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  box-shadow: var(--card-shadow);
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.competency-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-hover-shadow);
  border-color: var(--primary-color);
  background: linear-gradient(145deg, #ffffff, var(--light-background));
}

.competency-card h3 {
  font-size: 1.1rem;
  line-height: 1.4;
  margin: 0;
  color: var(--text-color);
  font-weight: 500;
}

.card-bg {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
}

.card-bg img {
  width: 100%;
  max-width: 150px;
  height: auto;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.competency-card:hover .card-bg img {
  transform: scale(1.1);
}

.card-content {
  width: 100%;
}

.card-content h3 {
  font-size: 1.2rem;
  color: var(--primary-color);
  margin: 0;
  line-height: 1.4;
}

/* Active Card Style */
.competency-card.active-card {
  border-color: var(--primary-color);
  background: linear-gradient(145deg, var(--light-background), #ffffff);
  box-shadow: 0 8px 15px rgba(var(--primary-color-rgb), 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .competencies-container {
    grid-template-columns: 1fr;
    padding: 20px;
  }

  .description-column {
    grid-column: 1;
    position: relative;
    top: 0;
    max-height: none;
    margin: 1rem auto;
  }
}

@media (max-width: 768px) {
  .section-titles {
    flex-direction: column;
    gap: 1rem;
  }

  .section-titles h2 {
    width: 100%;
    min-width: 0;
  }

  .section-titles::after {
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 20px;
    top: auto;
    bottom: -20px;
  }

  .main-title {
    font-size: 1.5rem;
    padding: 15px 30px;
  }
}
