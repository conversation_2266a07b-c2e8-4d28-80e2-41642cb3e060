/* team.css */
:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --font-family: 'Roboto', sans-serif;
  --text-color: #333;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
}
/* Main Content Styling */
main {
  margin-top: 100px; /* To account for fixed header */
}

/* Team Section */
.team-section {
  text-align: center;
  background: var(--light-background);
  padding: 60px 20px;
}

/* Section Title */
.team-section h2 {
  font-size: 2em;
  color: var(--primary-color);
  margin-bottom: 40px;
  position: relative;
  display: inline-block;
}

/* Decorative Line Under Title */
.team-section h2::after {
  content: '';
  display: block;
  width: 50px;
  height: 4px;
  background: var(--primary-color);
  margin: 10px auto 0;
}

/* Team Container */
.team-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 40px;
}

/* Team Member Card */
.team-member {
  flex-basis: calc(33.333% - 40px); /* Adjusted for gap */
  background: white;
  padding: 30px 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center; /* Center all content within the card */
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 74, 153, 0.1);
}

/* Team Photo */
.team-photo {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid var(--light-background);
  margin: 0 auto 20px;
  transition: transform 0.3s ease, border-color 0.3s ease;
}

.team-member:hover .team-photo {
  transform: scale(1.05);
  border-color: var(--primary-color);
}

/* Member Name */
.team-member h3 {
  margin: 0 0 5px 0; /* Remove top margin, add bottom margin */
  color: var(--text-color);
  font-size: 1.25em;
  font-weight: 600;
}

/* Role */
.role {
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: 15px;
}

/* Description */
.team-member p {
  font-size: 0.95em;
  color: #666;
  line-height: 1.6;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .team-member {
    flex-basis: calc(50% - 40px); /* Two columns on medium screens */
  }
}

@media (max-width: 768px) {
  .team-member {
    flex-basis: 100%; /* Full width on small screens */
  }
}
