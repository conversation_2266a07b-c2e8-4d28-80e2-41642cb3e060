<?php
header('Content-Type: application/json');
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/db_connect.php';

try {
    // Get and decode JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['code'])) {
        throw new Exception('No code provided');
    }

    $code = $input['code'];

    // Prepare and execute query
    $stmt = $conn->prepare("SELECT id, is_complete FROM users WHERE user_code = ?");
    $stmt->bind_param("s", $code);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Code nicht gefunden'
        ]);
        exit;
    }

    $user = $result->fetch_assoc();
    
    // Check if survey is complete
    if (!$user['is_complete']) {
        echo json_encode([
            'success' => false,
            'message' => 'Keine Ergebnisse verfügbar'
        ]);
        exit;
    }

    echo json_encode([
        'success' => true,
        'userId' => $user['id']
    ]);

} catch (Exception $e) {
    error_log($e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Ein Fehler ist aufgetreten'
    ]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>