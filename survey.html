<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Include Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <title>Open-Digi Umfrage</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" href="css/survey.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script>
  </head>
  <body>
    <header>
      <div class="header-content">
        <a href="index.html" class="logo-link">
          <img src="images/logo.png" alt="Open-Digi Logo" class="logo" />
        </a>
        <div class="user-menu">
          <span class="user-code"
            >Code: <span id="userCodeDisplay"></span
          ></span>
          <i class="fas fa-user-circle"></i>
          <button id="logoutButton">Logout</button>
        </div>
      </div>
    </header>

    <main>
      <div class="container">
        <div class="survey-header">
          <h2>Open-Digi Umfrage</h2>
          <div class="progress-container">
            <div id="progressBar">
              <div id="progressFill"></div>
            </div>
            <div id="progressText"></div>
          </div>
        </div>

        <!-- The form for survey T1, T2, T3 is dynamically populated by survey.js -->
        <form id="surveyForm" action="php/save_data.php" method="post">
          <!-- The dynamic content from `survey.js` will appear here -->
        </form>

        <!-- The T1/T2/T3 results section is also appended in `survey.js` once the user finishes -->
        <div id="results-section" class="results-section" style="display: none">
          <canvas id="competencyChart1"></canvas>
          <div id="descriptionBox1"></div>
          <div id="iliasLinks"></div>
        </div>
      </div>
    </main>

    <footer>
      <p>
        ©
        <script>
          document.write(new Date().getFullYear());
        </script>
        Open-Digi. Alle Rechte vorbehalten.
      </p>
    </footer>

    <!-- Scripts for the survey -->
    <script src="js/survey-data.min.js"></script>
    <script src="js/survey.min.js"></script>
    <script>
      /**
       * ⚙️ OVERRIDE getFormData to grab full label text
       * Paste this immediately after your survey.min.js include.
       */
      (function () {
        if (typeof getFormData !== "function") {
          console.warn("getFormData() not found—survey override not applied.");
          return;
        }
        const originalGetFormData = getFormData;

        window.getFormData = function () {
          console.log("⚙️ Running OVERRIDE getFormData for full-text options");
          const data = originalGetFormData();
          const form = document.querySelector("form#surveyForm");
          if (!form) return data;

          Object.keys(data).forEach((key) => {
            const inputs = form.querySelectorAll(`[name="${key}"]`);
            if (!inputs.length) return;

            // CHECKBOX GROUP
            if (inputs[0].type === "checkbox") {
              const full = [];
              inputs.forEach((input) => {
                if (input.checked) {
                  const lbl =
                    form.querySelector(`label[for="${input.id}"]`) ||
                    input.closest("label");
                  full.push(lbl ? lbl.textContent.trim() : input.value.trim());
                }
              });
              data[key] = full;
            }
            // RADIO GROUP
            else if (inputs[0].type === "radio") {
              const sel = form.querySelector(`[name="${key}"]:checked`);
              if (sel) {
                const lbl =
                  form.querySelector(`label[for="${sel.id}"]`) ||
                  sel.closest("label");
                data[key] = lbl ? lbl.textContent.trim() : sel.value.trim();
              }
            }
            // TEXT, SELECT, ETC → leave as-is
          });

          return data;
        };
      })();
    </script>

    <script src="js/script.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>

    <!-- Script to populate the user code -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Get the user code from sessionStorage
        const userCode =
          sessionStorage.getItem("generatedCode") ||
          sessionStorage.getItem("resultsCode");

        // Get the userId from sessionStorage
        const userId = sessionStorage.getItem("userId");

        // Display user code if available
        const userCodeDisplay = document.getElementById("userCodeDisplay");
        const userCodeContainer = document.querySelector(".user-code");

        if (userCode && userCodeDisplay) {
          userCodeDisplay.textContent = userCode;
        } else if (userId && userCodeDisplay) {
          // If no code but we have userId, try to fetch the code
          fetch(`php/get_user_data.php?userId=${userId}`)
            .then((response) => response.json())
            .then((data) => {
              if (data && data.userCode) {
                userCodeDisplay.textContent = data.userCode;
                // Store it for future use
                sessionStorage.setItem("resultsCode", data.userCode);
              } else {
                // Hide if no code is found
                if (userCodeContainer) userCodeContainer.style.display = "none";
              }
            })
            .catch((error) => {
              console.error("Error fetching user code:", error);
              if (userCodeContainer) userCodeContainer.style.display = "none";
            });
        } else {
          // Hide the code display if no code or userId is available
          if (userCodeContainer) userCodeContainer.style.display = "none";
        }
      });
    </script>
  </body>
</html>
