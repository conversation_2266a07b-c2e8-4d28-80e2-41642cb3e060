<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Include Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <title>Kreiere deinen persönlichen Zugangscode</title>
    <!-- Link to the CSS file -->
    <link rel="stylesheet" href="css/login.css" />
    <!-- Font Awesome for Icons (optional) -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- SweetAlert2 CSS for better alerts -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css"
    />
    <!-- Optional: Include SweetAlert2 JS for better alerts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  </head>
  <body>
    <div class="login-container">
      <a href="index.html">
        <img src="images/logo.png" alt="Open-Digi Logo" class="logo" />
      </a>
      <h1>Kreiere deinen persönlichen Zugangscode</h1>
      <p>Bitte achte dabei auf die Hinweise unter den Kästchen.</p>
      <form
        id="generateCodeForm"
        class="code-form"
        action="php/register.php"
        method="post"
      >
        <div class="input-group">
          <label for="birthplace">Geburtsort</label>
          <input
            type="text"
            id="birthplace"
            name="birthplace"
            minlength="2"
            maxlength="2"
            required
            placeholder=""
          />
          <small>
            Bitte geben Sie den <strong>ersten</strong> und
            <strong>letzten</strong> Buchstaben Ihres Geburtsortes ein. Bsp.:
            Bremen = BN
          </small>
        </div>
        <div class="input-group">
          <label id="parentLabel" for="parentName">
            Vorname der Mutter / Ihrer Erziehungsberechtigten
          </label>
          <input
            type="text"
            id="parentName"
            name="parentName"
            minlength="2"
            maxlength="2"
            required
            placeholder=""
          />
          <small id="parentInstructions">
            Bitte geben Sie den <strong>ersten</strong> und
            <strong>letzten</strong> Buchstaben des Vornamens Ihrer Mutter ein.
            Bsp.: Gisela = GA
          </small>
        </div>
        <div class="input-group">
          <label for="birthday">Geburtstag (TT)</label>
          <input
            type="text"
            id="birthday"
            name="birthday"
            pattern="\d{2}"
            minlength="2"
            maxlength="2"
            required
            placeholder=""
          />
          <small>
            Bitte geben Sie den <strong>Tag</strong> Ihres Geburtstages in
            Ziffern ein. Bsp: 06.12.1990 = Bitte nutzen Sie bspw. 06
          </small>
        </div>
        <div class="input-group">
          <label for="school">Ort der Grundschule</label>
          <input
            type="text"
            id="school"
            name="school"
            minlength="2"
            maxlength="2"
            required
            placeholder=""
          />
          <small>
            Bitte geben Sie den <strong>ersten</strong> und
            <strong>dritten</strong> Buchstaben des Ortes der Grundschule ein,
            die Sie als <strong>erstes</strong> besucht haben. Bsp.: GN
          </small>
        </div>
        <button type="submit" class="primary-button">Code Generieren</button>
      </form>
      <button
        class="primary-button"
        onclick="window.location.href = 'login.html'"
      >
        <!-- Changed to button -->
        Du hast schon einen personalsen Zugangscode? Hier geht es zum Login.
      </button>
    </div>
    <script src="js/survey-data.min.js"></script>
    <script src="js/script.min.js"></script>
  </body>
</html>
