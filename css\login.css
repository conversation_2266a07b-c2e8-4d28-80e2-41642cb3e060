/* login.css */

:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --font-family: '<PERSON>o', sans-serif;
  --text-color: #333;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
}
/* --- General Styles --- */

body {
  margin: 0; /* Remove default body margin */
  padding: 0;
  font-family: 'Roboto', sans-serif; /* Use the correct font-family */
  background-color: #f0f2f5;
  display: flex; /* Use flexbox to center content vertically */
  align-items: center; /* Center vertically */
  justify-content: center; /* Center horizontally */
  min-height: 100vh; /* Ensure it covers the full viewport height */
}

/* --- Container Styles --- */

.login-container {
  max-width: 600px;
  margin: 0 auto; /*  Centering with margin auto */
  padding: 2rem; /*  Using rem for padding */
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.logo {
  display: block;
  margin: 0 auto 2rem; /* Reduced margin */
  max-width: 150px; /* Smaller logo */
  height: auto; /* Maintain aspect ratio */
}

/* --- Form Group Styles --- */

.form-group {
  margin-bottom: 1.5rem; /* Reduced margin */
  text-align: center;
}

.form-group legend {
  font-size: 1.5rem; /* Reduced font size */
  font-weight: bold;
  margin-bottom: 1rem; /* Reduced margin */
  color: #333;
}

/* --- Styles for Choice Cards --- */

.choice-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 1rem; /* Reduced gap */
  margin-bottom: 1.5rem; /* Reduced margin */
}

.choice-card {
  flex: 1 1 calc(33% - 1rem); /* Adjusted for gap */
  padding: 1.25rem; /* Reduced padding */
  border: 2px solid #ccc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background-color: #f9f9f9;
}

.choice-card:hover {
  border-color: var(--primary-color);
  background-color: #e0f0ff; /* Use variable */
  transform: translateY(-5px);
}

.choice-card.selected {
  border-color: var(--primary-color);
  background-color: var(--light-background); /* Use variable */
  box-shadow: 0 0 10px rgba(0, 74, 153, 0.2);
}

.choice-card h3 {
  margin: 0;
  font-size: 1.25rem; /* Reduced font size */
  color: var(--primary-color);
}

.choice-card p {
  margin: 0.625rem 0 0; /* Reduced margin */
  font-size: 1rem; /* Reduced font size */
  color: #333;
}

.card-icon {
  font-size: 2.5rem; /* Reduced font size */
  color: var(--primary-color);
  margin-bottom: 1rem; /* Reduced margin */
}

/* --- Input Group Styles --- */

.input-group {
  margin-bottom: 1.25rem; /* Reduced margin */
  text-align: left;
}

.input-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem; /* Reduced margin */
  font-size: 1rem; /* Reduced font size */
}

.input-group input,
.input-group textarea {
  width: 100%;
  padding: 0.625rem; /* Reduced padding */
  font-size: 1rem; /* Reduced font size */
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  transition: border-color 0.2s;
  margin-top: 0.3125rem; /* Reduced margin */
}

.input-group input:focus,
.input-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-group small {
  font-size: 0.75rem; /* Reduced font size */
  color: #666;
  margin-top: 0.3125rem; /* Reduced margin */
}

/* --- Button Styling --- */

.primary-button {
  width: 100%;
  padding: 0.75rem 1.25rem; /* Reduced padding */
  font-size: 1rem; /* Reduced font size */
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.1s;
  margin-top: 1.25rem; /* Reduced margin */
  display: inline-block;
}

.primary-button:hover {
  background-color: var(--secondary-color);
  transform: scale(1.02);
}

.primary-button:active {
  transform: scale(1);
}

/* --- Code Display Styling --- */

.code-display {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.25rem; /* Reduced padding */
  margin: 1.5rem 0; /* Reduced margin */
  background-color: #e8f0fe;
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  font-size: 1.5rem; /* Reduced font size */
  font-weight: bold;
  color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#codeText {
  font-family: monospace;
}

/* --- Responsive Design --- */

@media (max-width: 767px) {
  .choice-container {
    flex-direction: column;
  }

  .choice-card {
    flex: 1 1 100%;
  }
  .login-container {
    margin: 50px 10px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 1rem; /* Reduced padding */
  }

  .choice-card {
    padding: 1rem; /* Reduced padding */
  }

  .choice-card h3 {
    font-size: 1.125rem; /* Reduced font size */
  }

  .choice-card p {
    font-size: 0.875rem; /* Reduced font size */
  }
  .form-group legend {
    font-size: 1.2rem;
  }
}
