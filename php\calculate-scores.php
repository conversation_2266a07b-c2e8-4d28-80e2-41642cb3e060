<?php
function calculateCompetencyScores($responses) {
    $scores = [];
    $total = 0;
    $count = 0;

    // Define competency categories and their corresponding NEW question prefixes
    // Original q1_ -> now q2_, q2_ -> now q3_, etc.
    $competencyCategories = [
        'Suchen, Verarbeiten und Aufbewahren' => ['q2_'], // Was q1_
        'Kommunikation und Kollaborieren' => ['q3_'],     // Was q2_
        'Produzieren und Präsentieren' => ['q4_'],        // Was q3_
        'Schützen und sicher Agieren' => ['q5_'],         // Was q4_
        'Problemlösen und Handeln' => ['q6_'],            // Was q5_
        'Analysieren und Reflektieren' => ['q7_']         // Was q6_
    ];

    foreach ($competencyCategories as $category => $prefixes) {
        $categoryScore = 0;
        $categoryCount = 0;

        foreach ($responses as $questionId => $response) {
            // Only consider the original scale questions for scoring
            // Check if the question ID matches the pattern for original scale questions (e.g., q2_0 to q2_5, q3_0 to q3_6, etc.)
            // This regex assumes original questions ended before _6, _7, _11 etc. Adjust if needed.
             if (preg_match('/^q[2-7]_\d$/', $questionId)) { // Basic check for original scale questions in sections 2-7
                 foreach ($prefixes as $prefix) {
                     if (strpos($questionId, $prefix) === 0 && is_numeric($response)) {
                         $categoryScore += intval($response);
                         $categoryCount++;
                         break; // Found matching prefix for this question
                     }
                 }
             }
        }

        // Calculate average score for the category (0-100 scale)
        // Assuming scale is 0-6 (7 points)
        $maxScaleValue = 6;
        $scores[$category] = $categoryCount > 0 ? round(($categoryScore / $categoryCount) / $maxScaleValue * 100) : 0;
        $total += $categoryScore;
        $count += $categoryCount;
    }

    // Calculate overall score based only on the original competency questions
    $scores['overall'] = $count > 0 ? round(($total / $count) / $maxScaleValue * 100) : 0;

    return $scores;
}

// Function to get all scores for a user (No changes needed here)
function getAllUserScores($conn, $userId) {
    // ... (keep existing implementation) ...
     $allScores = [
        'initialScores' => [],
        'updatedScores' => [],
        'followUpScores' => []
    ];

    $query = "SELECT attempt_number, category, score FROM scores WHERE user_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $attemptNumber = $row['attempt_number'];
        $category = $row['category'];
        $score = $row['score'];

        if ($attemptNumber == 1) {
            $allScores['initialScores'][$category] = $score;
        } else if ($attemptNumber == 2) {
            $allScores['updatedScores'][$category] = $score;
        } else if ($attemptNumber == 3) {
            $allScores['followUpScores'][$category] = $score;
        }
    }

    $stmt->close();
    return $allScores;
}
?>