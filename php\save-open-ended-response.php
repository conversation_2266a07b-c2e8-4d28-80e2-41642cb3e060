<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

require_once __DIR__ . '/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get JSON data from request body
        $jsonData = file_get_contents('php://input');
        $data = json_decode($jsonData, true);
        
        if (!$data) {
            throw new Exception("Invalid JSON data");
        }
        
        $userId = isset($data['userId']) ? (int)$data['userId'] : 0;
        $key = isset($data['key']) ? $data['key'] : '';
        $response = isset($data['response']) ? $data['response'] : '';
        $attempt = isset($data['attempt']) ? (int)$data['attempt'] : 1;
        
        // Validate data
        if ($userId <= 0) {
            throw new Exception("Invalid userId: $userId");
        }
        
        if (empty($key)) {
            throw new Exception("Question key cannot be empty");
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        // Log the data being saved for debugging
        error_log("Saving open-ended response: userId=$userId, key=$key, attempt=$attempt, response length=" . strlen($response));
        
        // Delete any existing response with the same key and user
        $delete_query = "DELETE FROM open_ended_responses WHERE user_id = ? AND question_key = ? AND attempt = ?";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bind_param("isi", $userId, $key, $attempt);
        $delete_stmt->execute();
        $delete_stmt->close();
        
        // Insert the new response
        $insert_query = "INSERT INTO open_ended_responses (user_id, question_key, response, attempt) VALUES (?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("issi", $userId, $key, $response, $attempt);
        $insert_stmt->execute();
        $insert_stmt->close();
        
        // Commit transaction
        $conn->commit();
        
        echo json_encode(['success' => true, 'message' => 'Response saved successfully']);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if (isset($conn) && $conn->ping()) {
            $conn->rollback();
        }
        
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Only POST requests are allowed']);
}
?>