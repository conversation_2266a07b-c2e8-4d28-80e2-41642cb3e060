/* index.css */
:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --font-family: 'Roboto', sans-serif;
  --text-color: #333;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
}
/* Adjust Main Content Margin */
main {
  margin-top: 100px; /* Account for fixed header */
  padding: 0 20px;
}

/* Banner Container */
.banner-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 30px;
  max-width: 1400px;
  margin: 50px auto 0;
}

/* Banner Section Styles */
.banner-section {
  flex: 1;
  background-color: var(--light-background);
  border-radius: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 30px;
  box-sizing: border-box;
  position: relative;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid rgba(0, 74, 153, 0.1);
}

/* Hover Effect */
.banner-section:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 30px rgba(0, 74, 153, 0.15);
  border-color: var(--primary-color);
}

/* Decorative Elements */
.banner-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.banner-section:hover::before {
  transform: scaleX(1);
}

/* Overlay */
.banner-section .overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-grow: 1;
  width: 100%;
}

/* Images */
.banner-section img {
  width: 180px;
  height: 180px;
  object-fit: contain;
  margin-bottom: 30px;
  transition: transform 0.5s ease;
  animation: float 3s ease-in-out infinite;
}

.banner-section:hover img {
  transform: scale(1.05);
}

/* Animation for floating effect */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Titles */
.banner-section h2 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.8em;
  font-weight: 700;
  transition: transform 0.3s ease;
}

.banner-section:hover h2 {
  transform: scale(1.05);
}

/* Descriptions */
.banner-section p {
  flex-grow: 1;
  margin-bottom: 30px;
  text-align: center;
  font-size: 1.1em;
  color: var(--text-color);
  line-height: 1.7;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.banner-section:hover p {
  opacity: 1;
}

/* Buttons */
.section-button {
  margin-top: auto;
  background-color: var(--primary-color);
  color: #fff;
  border: none;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.section-button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 74, 153, 0.2);
}

.section-button:active {
  transform: translateY(0);
}

/* Add ripple effect to buttons */
.section-button::after {
  /*Removed as it was not working properly*/
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.section-button:hover::after {
  width: 300px;
  height: 300px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .banner-container {
    padding: 0 20px;
  }

  .banner-section img {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 900px) {
  .banner-container {
    flex-direction: column;
  }

  .banner-section {
    margin-bottom: 20px;
  }

  .banner-section img {
    width: 140px;
    height: 140px;
  }
}

@media (max-width: 480px) {
  main {
    margin-top: 80px;
  }

  .banner-section {
    padding: 30px 20px;
  }

  .banner-section img {
    width: 120px;
    height: 120px;
  }

  .banner-section h2 {
    font-size: 1.5em;
  }

  .banner-section p {
    font-size: 1em;
  }
}
