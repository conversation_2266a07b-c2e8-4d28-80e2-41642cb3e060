<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ihre Ergebnisse - Open-Digi</title>
    <!-- Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap"
      rel="stylesheet"
    />
    <!-- FontAwesome Icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- SweetAlert2 CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" href="css/survey.css" />
    <style>
      .results-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 20px;
      }
      .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 40px;
      }
      .competency-description {
        margin-top: 20px;
        background: #f0f0f0;
        padding: 15px;
        border-radius: 5px;
      }
      .strategy-block {
        margin-top: 30px;
        background: #eef7ff;
        border: 1px solid #cceeff;
        padding: 15px;
        border-radius: 5px;
      }
      .ilias-links {
        margin-top: 30px;
        background: #fff7e6;
        border: 1px solid #ffecb3;
        border-radius: 5px;
        padding: 15px;
      }
      .ilias-links ul {
        margin-left: 20px;
      }
      .user-code-display {
        font-style: italic;
        color: #555;
      }
      /* Button styling for PDF export */
      .export-button {
        margin-top: 20px;
        background-color: #4caf50;
        color: white;
        border: none;
        padding: 10px 20px;
        cursor: pointer;
        font-size: 16px;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <header>
      <div class="header-content">
        <a href="index.html" class="logo-link">
          <img src="images/logo.png" alt="Open-Digi Logo" class="logo" />
        </a>
        <div class="user-menu">
          <span class="user-code"
            >Code: <span id="userCodeDisplay"></span
          ></span>
          <i class="fas fa-user-circle"></i>
          <button id="logoutButton">Logout</button>
        </div>
      </div>
    </header>

    <main>
      <div class="results-container">
        <h1>Ihre bisherigen Ergebnisse</h1>
        <p>
          Willkommen! Hier sehen Sie Ihre Kompetenzstände für die erste, zweite
          und dritte Befragung (sofern vorhanden)
        </p>

        <!-- Show code from sessionStorage if available -->
        <p class="user-code-display" id="userCodeLine" style="display: none">
          Code: <span id="codeValue"></span>
        </p>

        <!-- Hidden form element needed for survey.min.js showResults function -->
        <form id="surveyForm" style="display: none"></form>

        <!-- The dynamic chart -->
        <div class="chart-container">
          <canvas id="resultsChart"></canvas>
        </div>

        <!-- Hover info box -->
        <div class="competency-description" id="resultsDescriptionBox">
          <h3>Kompetenzbereich</h3>
          <p>Bewegen Sie die Maus über die Balken, um Details zu sehen.</p>
        </div>

        <!-- T1 Strategy, if any -->
        <div class="strategy-block" id="t1StrategyBlock" style="display: none">
          <h3>Strategie zur Kursauswahl</h3>
          <p id="t1StrategyText"></p>
        </div>

        <!-- Single ILIAS link box -->
        <div class="ilias-links">
          <a
            href="https://ilias.uni-rostock.de/goto.php?target=crs_135810&client_id=ilias_hro"
            target="_blank"
            class="course-link-box"
          >
            <div class="course-link-content">
              <h3>
                Hier haben Sie direkten Zugriff auf die Mikrofortbildungen für
                alle Kompetenzbereiche
              </h3>
              <span class="click-hint"
                >Klicken Sie hier, um zu den Kursen zu gelangen</span
              >
            </div>
          </a>
        </div>

        <!-- NEW: PDF Export button -->
        <button id="exportPdfBtn" class="export-button">
          Ergebnisse als PDF speichern
        </button>
      </div>
    </main>

    <footer>
      <p>
        ©
        <script>
          document.write(new Date().getFullYear());
        </script>
        Open-Digi. Alle Rechte vorbehalten.
      </p>
    </footer>

    <!-- Scripts: Chart.js, SweetAlert2, plus your existing minified scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="js/survey-data.min.js"></script>
    <script src="js/script.min.js"></script>
    <!-- Make sure this is included BEFORE survey.min.js -->
    <script src="js/survey.min.js"></script>
    <!-- Include user code display script -->
    <script src="js/user-code-display.js"></script>

    <!-- html2canvas & jsPDF for PDF export -->
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>

    <script>
      document.addEventListener("DOMContentLoaded", async () => {
        const userId = sessionStorage.getItem("userId");
        if (!userId) {
          Swal.fire({
            icon: "error",
            title: "Fehler",
            text: "Keine Benutzer-ID gefunden. Bitte erneut einloggen.",
          }).then(() => {
            window.location.href = "login.html";
          });
          return;
        }

        const storedCode = sessionStorage.getItem("resultsCode");
        if (storedCode) {
          document.getElementById("userCodeLine").style.display = "block";
          document.getElementById("codeValue").textContent = storedCode;
        }

        try {
          const res = await fetch(`php/get_user_data.php?userId=${userId}`);
          if (!res.ok) {
            throw new Error("Fehler beim Abrufen der User-Daten");
          }
          const userData = await res.json();

          const {
            initialScores = {},
            updatedScores = {},
            followUpScores = {},
            openEndedResponses = {},
          } = userData;

          // Show T1 strategy if any
          if (openEndedResponses && openEndedResponses.t1_strategy) {
            document.getElementById("t1StrategyBlock").style.display = "block";
            document.getElementById("t1StrategyText").textContent =
              openEndedResponses.t1_strategy;
          }

          // Build chart data
          const allCats = new Set([
            ...Object.keys(initialScores),
            ...Object.keys(updatedScores),
            ...Object.keys(followUpScores),
          ]);
          allCats.delete("overall");
          // Sort categories in the specified order using the sortCategories function from script.min.js
          const categories = sortCategories(Array.from(allCats));

          const t1Data = categories.map((cat) => initialScores[cat] || 0);
          const t2Data = categories.map((cat) => updatedScores[cat] || 0);
          const t3Data = categories.map((cat) => followUpScores[cat] || 0);

          // Retrieve userCode from sessionStorage or use 'Unknown' as default value if not found
          const userCode = userData.userCode || "Unknown";

          // Create the chart using the shared configuration function
          const chartConfig = createCompetencyChartConfig(
            categories,
            t1Data,
            t2Data,
            t3Data,
            "resultsChart",
            "resultsDescriptionBox",
            userCode
          );
          new Chart(
            document.getElementById("resultsChart").getContext("2d"),
            chartConfig
          );
        } catch (err) {
          console.error("Error loading results:", err);
          Swal.fire({
            icon: "error",
            title: "Fehler",
            text: "Ergebnisse konnten nicht geladen werden.",
          });
        }
      });

      // ======================================
      // PDF Export Setup
      // ======================================
      document.addEventListener("DOMContentLoaded", () => {
        const exportBtn = document.getElementById("exportPdfBtn");
        const resultsContainer = document.querySelector(".results-container");

        if (exportBtn && resultsContainer) {
          exportBtn.addEventListener("click", async () => {
            const { jsPDF } = window.jspdf;
            try {
              const canvas = await html2canvas(resultsContainer, { scale: 2 });
              const imgData = canvas.toDataURL("image/png");

              const pdf = new jsPDF("p", "pt", "a4");
              const pageWidth = pdf.internal.pageSize.getWidth();
              const pageHeight = pdf.internal.pageSize.getHeight();

              const canvasWidth = canvas.width;
              const canvasHeight = canvas.height;
              const ratio = Math.min(
                pageWidth / canvasWidth,
                pageHeight / canvasHeight
              );

              const imgWidth = canvasWidth * ratio;
              const imgHeight = canvasHeight * ratio;

              pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
              pdf.save("OpenDigi_Ergebnisse.pdf");
            } catch (err) {
              console.error("PDF export error:", err);
              Swal.fire({
                icon: "error",
                title: "Fehler",
                text: "PDF konnte nicht erstellt werden.",
              });
            }
          });
        }
      });
    </script>
  </body>
</html>
