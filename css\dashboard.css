/* dashboard.css */

:root {
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-info: #0dcaf0;
  --bs-border-color: #dee2e6;
  --table-header-bg: #e9ecef;
  --table-row-odd-bg: #f8f9fa;
  --font-family-sans-serif: system-ui, -apple-system, 'Segoe UI', Roboto,
    'Helvetica Neue', <PERSON>l, 'Noto Sans', 'Liberation Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

body {
  background-color: var(--bs-light);
  font-family: var(--font-family-sans-serif);
  color: var(--bs-dark);
  font-size: 0.95rem;
}

.container-fluid {
  padding: 15px;
}

h1.dashboard-title {
  color: var(--bs-primary);
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--bs-primary);
  padding-bottom: 0.5rem;
  display: inline-block;
}

.controls-panel,
.filter-panel {
  background-color: #ffffff;
  padding: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
}

/* --- Table Styles --- */
.table-responsive {
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  background-color: #fff;
  max-height: 75vh;
  overflow: auto;
}

#userTable {
  margin-bottom: 0;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 0.9rem;
  table-layout: auto; /* Allow browser to determine column widths */
}

#userTable th,
#userTable td {
  padding: 0.6rem 0.75rem;
  vertical-align: top;
  border-bottom: 1px solid var(--bs-border-color);
  border-right: 1px solid var(--bs-border-color);
  white-space: nowrap; /* Keep content on one line initially */
}

#userTable th:last-child,
#userTable td:last-child {
  border-right: 0;
}

/* --- Column Width Optimization --- */
#userTable th:first-child,
#userTable td:first-child {
  width: 40px; /* Fixed small width for checkbox column */
  min-width: 40px;
  text-align: center;
  border-left: 0;
}

#userTable th,
#userTable td {
  width: auto; /* Let other columns size themselves */
  min-width: 150px; /* But have a reasonable minimum width */
  max-width: 350px; /* And a maximum to prevent very wide columns */
}

#userTable tbody tr:nth-child(odd) td {
  background-color: var(--table-row-odd-bg);
}
#userTable tbody tr:hover td {
  background-color: #e2e6ea;
}

#userTable thead th {
  background-color: var(--table-header-bg);
  border-bottom: 2px solid var(--bs-border-color);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

/* --- Cell Expansion Fix --- */
#userTable td {
  position: relative; /* Crucial for positioning the popup */
}

.expandable-cell {
  display: flex;
  align-items: baseline;
  overflow: hidden;
}

.cell-truncated-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.expand-toggle-btn {
  margin-left: 4px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.85em;
  padding: 0 2px;
  color: var(--bs-primary);
  line-height: 1;
  flex-shrink: 0;
}

/* --- Cell Expansion Fix --- */
.cell-full {
  /* This is now our shared tooltip */
  display: none;
  position: absolute; /* We will control top/left with JS */
  min-width: 250px;
  max-width: 500px;
  background: #fff;
  border: 1px solid #adb5bd;
  padding: 12px;
  z-index: 1050; /* High z-index to be on top of everything */
  white-space: normal;
  word-wrap: break-word;
  border-radius: 0.25rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  font-size: 0.9rem;
  line-height: 1.5;
  pointer-events: none; /* So it doesn't interfere with other clicks */
}

.cell-full-active {
  display: block !important;
}

/* --- Other Styles (Pagination, Buttons, etc.) --- */
.btn,
.input-group .form-control,
.input-group-text {
  font-size: 0.9rem;
}

#pagination {
  margin-top: 1.5rem;
  padding: 0.5rem 0;
}

.visualization-sidebar {
  position: fixed;
  top: 0;
  right: -450px;
  width: 450px;
  height: 100vh;
  background: #fff;
  box-shadow: -0.5rem 0 1rem rgba(0, 0, 0, 0.15);
  transition: right 0.35s cubic-bezier(0.25, 0.1, 0.25, 1);
  z-index: 1050;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--bs-border-color);
}
.visualization-sidebar.active {
  right: 0;
}
.visualization-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--bs-border-color);
  background: var(--table-header-bg);
}
.visualization-content {
  flex-grow: 1;
  padding: 1rem;
  overflow-y: auto;
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  z-index: 1040;
  display: none;
  transition: background 0.35s ease;
}
.overlay.active {
  display: block;
  background: rgba(0, 0, 0, 0.4);
}

/* --- Stacking Context Fix for Popups --- */
#userTable tbody tr:hover {
  z-index: 5; /* Bring the hovered row to the front */
  position: relative; /* Required for z-index to take effect on table rows */
}