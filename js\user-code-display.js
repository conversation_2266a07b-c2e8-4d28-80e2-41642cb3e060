/**
 * user-code-display.js
 * Handles the display of user codes in the header across all pages
 */

document.addEventListener('DOMContentLoaded', function () {
  // Get the user code from sessionStorage
  const userCode =
    sessionStorage.getItem('generatedCode') ||
    sessionStorage.getItem('resultsCode')

  // Display it if available
  if (userCode && document.getElementById('userCodeDisplay')) {
    document.getElementById('userCodeDisplay').textContent = userCode
  } else {
    // Hide the code display if no code is available
    const codeDisplay = document.querySelector('.user-code')
    if (codeDisplay) {
      codeDisplay.style.display = 'none'
    }
  }
})
