<?php
// --- Error Reporting & Headers ---
ini_set('display_errors', 0);
error_reporting(E_ALL);
ini_set('log_errors', '1');
header('Content-Type: application/json');
session_start();

// --- AUTHENTICATION CHECK ---
$isAuthenticated = false;
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    $isAuthenticated = true;
}
if (!$isAuthenticated) {
    http_response_code(401);
    echo json_encode(['error' => true, 'message' => 'Unauthorized - Please login']);
    exit;
}
// --- END AUTHENTICATION CHECK ---

try {
    require_once __DIR__ . '/db_connect.php';

    // --- Load Survey Structure ---
    $survey_data_path = __DIR__ . '/../js/survey-data.min.js';
    $all_possible_question_ids = [];
    if (file_exists($survey_data_path)) {
        $js_content = @file_get_contents($survey_data_path);
        if ($js_content !== false) {
            preg_match_all('/id:\s*["\'](q\d+_\d+)["\']/', $js_content, $matches);
            if (!empty($matches[1])) {
                $all_possible_question_ids = array_unique($matches[1]);
            }
        }
    }

    // --- Data fetching logic ---
    $users = [];
    $db_question_ids = [];

    $query = "SELECT id, user_code, attempt_number, is_complete, datenschutz_consent, unterschrift,
                     t1_startTimestamp, t1_endTimestamp,
                     t2_startTimestamp, t2_endTimestamp,
                     t3_startTimestamp, t3_endTimestamp
              FROM users";
    $result = $conn->query($query);
    if (!$result) {
        error_log("Error fetching users: " . $conn->error);
        throw new Exception("Error fetching users data.");
    }
    while ($row = $result->fetch_assoc()) {
        $userId = $row['id'];
        $users[$userId] = $row;
        $users[$userId]['datenschutzConsent'] = (bool)$row['datenschutz_consent'];
        $users[$userId]['userCode'] = $row['user_code'];
        $users[$userId]['timeStamps'] = [ // New structure
            't1' => ['start' => $row['t1_startTimestamp'], 'end' => $row['t1_endTimestamp']],
            't2' => ['start' => $row['t2_startTimestamp'], 'end' => $row['t2_endTimestamp']],
            't3' => ['start' => $row['t3_startTimestamp'], 'end' => $row['t3_endTimestamp']],
        ];
        $users[$userId]['initialResponses'] = [];
        $users[$userId]['updatedResponses'] = [];
        $users[$userId]['followUpResponses'] = [];
        $users[$userId]['preSurveyResponses'] = [];
        $users[$userId]['openEndedResponses'] = []; // Store all attempts if needed
        $users[$userId]['initialScores'] = [];
        $users[$userId]['updatedScores'] = [];
        $users[$userId]['followUpScores'] = [];
    }
    $userIds = array_keys($users);

    $questionIdQuery = "SELECT DISTINCT question_id FROM responses ORDER BY question_id";
    $questionIdResult = $conn->query($questionIdQuery);
    if ($questionIdResult) {
        while ($qRow = $questionIdResult->fetch_assoc()) {
            if (preg_match('/^q\d+_\d+$/', $qRow['question_id'])) {
                $db_question_ids[] = $qRow['question_id'];
            }
        }
    }

    $final_question_ids = array_unique(array_merge($all_possible_question_ids, $db_question_ids));
    sort($final_question_ids);

    if (!empty($userIds)) {
        $userIdPlaceholders = implode(',', array_fill(0, count($userIds), '?'));
        $userIdTypes = str_repeat('i', count($userIds));

        // Fetch Responses
        $responseQuery = "SELECT user_id, question_id, response, attempt FROM responses WHERE user_id IN ($userIdPlaceholders)";
        $stmt = $conn->prepare($responseQuery);
        if ($stmt) {
            $stmt->bind_param($userIdTypes, ...$userIds);
            $stmt->execute();
            $responseResult = $stmt->get_result();
            while ($row = $responseResult->fetch_assoc()) {
                if (isset($users[$row['user_id']])) {
                    $userId = $row['user_id'];
                    $attempt = $row['attempt'];
                    if ($attempt == 1) $users[$userId]['initialResponses'][$row['question_id']] = $row['response'];
                    elseif ($attempt == 2) $users[$userId]['updatedResponses'][$row['question_id']] = $row['response'];
                    elseif ($attempt == 3) $users[$userId]['followUpResponses'][$row['question_id']] = $row['response'];
                }
            }
            $stmt->close();
        }


        // Fetch Pre-Survey Responses
        $preSurveyQuery = "SELECT user_id, question_id, response FROM pre_survey_responses WHERE user_id IN ($userIdPlaceholders)";
        $stmt = $conn->prepare($preSurveyQuery);
        if ($stmt) {
            $stmt->bind_param($userIdTypes, ...$userIds);
            $stmt->execute();
            $preSurveyResult = $stmt->get_result();
            while ($row = $preSurveyResult->fetch_assoc()) {
                if (isset($users[$row['user_id']])) {
                    $users[$row['user_id']]['preSurveyResponses'][$row['question_id']] = $row['response'];
                }
            }
            $stmt->close();
        }

        // Fetch Open-Ended Responses (Adjust if you need per-attempt OER for dashboard)
        $openEndedQuery = "SELECT user_id, question_key, response, attempt FROM open_ended_responses WHERE user_id IN ($userIdPlaceholders)";
        $stmt = $conn->prepare($openEndedQuery);
        if ($stmt) {
            $stmt->bind_param($userIdTypes, ...$userIds);
            $stmt->execute();
            $openEndedResult = $stmt->get_result();
            while ($row = $openEndedResult->fetch_assoc()) {
                if (isset($users[$row['user_id']])) {
                    // This stores the LATEST response for a given key if multiple attempts exist.
                    // If you need all attempts, you'll need to structure $users[$userId]['openEndedResponses'] as an array or object keyed by attempt.
                    // For now, keeping it simple for dashboard display of potentially one OER per key.
                    $users[$row['user_id']]['openEndedResponses'][$row['question_key']] = $row['response'];
                }
            }
            $stmt->close();
        }


        // Fetch Scores
        $scoresQuery = "SELECT user_id, attempt_number, category, score FROM scores WHERE user_id IN ($userIdPlaceholders)";
        $stmt = $conn->prepare($scoresQuery);
        if ($stmt) {
            $stmt->bind_param($userIdTypes, ...$userIds);
            $stmt->execute();
            $scoresResult = $stmt->get_result();
            while ($row = $scoresResult->fetch_assoc()) {
                if (isset($users[$row['user_id']])) {
                    $userId = $row['user_id'];
                    $attemptNumber = $row['attempt_number'];
                    if ($attemptNumber == 1) $users[$userId]['initialScores'][$row['category']] = (float)$row['score'];
                    elseif ($attemptNumber == 2) $users[$userId]['updatedScores'][$row['category']] = (float)$row['score'];
                    elseif ($attemptNumber == 3) $users[$userId]['followUpScores'][$row['category']] = (float)$row['score'];
                }
            }
            $stmt->close();
        }
    }

    $usersList = array_values($users);
    echo json_encode(['users' => $usersList, 'questionIds' => $final_question_ids]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Dashboard Data Error: " . $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString());
    echo json_encode([
        'error' => true,
        'message' => 'An internal server error occurred while fetching dashboard data.'
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli) {
        $conn->close();
    }
}
?>