<?php
require_once __DIR__ . '/db_connect.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $code = isset($_POST['code']) ? mb_strtoupper(trim($_POST['code']), 'UTF-8') : '';
    $code = trim($code); // Trim whitespace!
    $startNewAttempt = isset($_POST['startNewAttempt']) && $_POST['startNewAttempt'] === 'true';
    $attemptType = isset($_POST['attemptType']) ? $_POST['attemptType'] : null;

    // Debugging: Dump the received code
    // var_dump($_POST); // Keep this commented out unless actively debugging

    $query = "SELECT id, attempt_number FROM users WHERE UPPER(user_code) = ?"; // Use UPPER()
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $code);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        $userId = $user['id'];
        $attemptNumber = $user['attempt_number'];

        // Debugging: Dump the fetched user data
        // var_dump($user); // Keep this commented out unless actively debugging

        // Check if userId is valid (extra safety)
        if (!is_numeric($userId) || $userId <= 0) {
            echo json_encode(["ok" => false, "message" => "Invalid user ID fetched."]);
            $stmt->close();
            $conn->close();
            exit();
        }


        if ($startNewAttempt) {
            if ($attemptType === 'T2') {
                $attemptNumber = 2;
            } elseif ($attemptType === 'T3') {
                $attemptNumber = 3;
            } else {
                $attemptNumber = 1; // Reset to 1 if no type specified
            }

            // Update the attempt number in the database
            $update_query = "UPDATE users SET attempt_number = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("ii", $attemptNumber, $userId);
            $update_stmt->execute();
            $update_stmt->close();
        }

        echo json_encode([
            "ok" => true,
            "userId" => $userId,
            "attemptNumber" => $attemptNumber,
            "message" => "Login successful"
        ]);
    } else {
        echo json_encode(["ok" => false, "message" => "Invalid code"]);
    }

    $stmt->close();
    $conn->close();
} else {
    echo json_encode(["ok" => false, "message" => "Invalid request method"]);
}
?>