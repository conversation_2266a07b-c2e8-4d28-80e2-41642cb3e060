/* dashboard.css */

:root {
  /* Using Bootstrap's default theme colors for this example,
     but you can override with your --primary-color etc. if preferred */
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-info: #0dcaf0;
  --bs-border-color: #dee2e6;
  --table-header-bg: #e9ecef;
  --table-row-odd-bg: #f8f9fa;
  --font-family-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  background-color: var(--bs-light);
  font-family: var(--font-family-sans-serif);
  color: var(--bs-dark);
  font-size: 0.95rem; /* Slightly smaller base font for denser UI */
}

.container-fluid {
  padding: 15px;
}

h1.dashboard-title { /* Added a class for specific styling */
  color: var(--bs-primary);
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--bs-primary);
  padding-bottom: 0.5rem;
  display: inline-block;
}

/* Top control panel styling */
.controls-panel {
  background-color: #ffffff;
  padding: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
}

.filter-panel {
  background-color: #ffffff;
  padding: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}


/* Table Styles */
.table-responsive {
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem; /* Bootstrap's default card/table radius */
  background-color: #fff;
  max-height: 75vh; /* Allow table body to scroll */
  overflow: auto; /* Handles both x and y scroll */
}

#userTable {
  margin-bottom: 0;
  width: 100%;
  border-collapse: separate; /* Allows border-spacing */
  border-spacing: 0; /* Remove default spacing */
  font-size: 0.9rem; /* Slightly smaller table font */
}

#userTable th,
#userTable td {
  white-space: nowrap;
  padding: 0.6rem 0.75rem; /* Adjusted padding */
  vertical-align: top; /* Changed to top for better alignment with multi-line potential */
  border-bottom: 1px solid var(--bs-border-color);
  border-right: 1px solid var(--bs-border-color); /* Add right border for grid look */
}
#userTable th:first-child,
#userTable td:first-child {
    border-left: 0; /* Remove left border on first cell of a row */
}
#userTable th:last-child,
#userTable td:last-child {
    border-right: 0; /* Remove right border on last cell of a row */
}


#userTable tbody tr:nth-child(odd) td { /* Target td for odd rows */
  background-color: var(--table-row-odd-bg);
}
#userTable tbody tr:hover td { /* Hover effect for rows */
  background-color: #e2e6ea;
}


#userTable thead th {
  background-color: var(--table-header-bg);
  border-bottom: 2px solid var(--bs-border-color);
  border-right: 1px solid var(--bs-border-color);
  position: -webkit-sticky; /* For Safari */
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: inset 0 -2px 0 rgba(0,0,0,0.05); /* Subtle inner shadow */
  font-weight: 600; /* Bolder headers */
}
/* If you have a fixed top bar above the dashboard title, adjust 'top' */
/* e.g., body.dashboard-fixed-top #userTable thead th { top: 56px; } */


/* Button and Control Styles */
.btn {
  font-size: 0.9rem; /* Slightly smaller buttons */
}
.btn-sm { /* Ensure btn-sm is effective */
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.input-group .form-control {
    font-size: 0.9rem;
}
.input-group-text {
    font-size: 0.9rem;
    background-color: #e9ecef;
}


/* Pagination Styles */
#pagination {
  margin-top: 1.5rem;
  padding: 0.5rem 0;
}
.pagination-info {
  color: var(--bs-secondary);
  font-size: 0.9rem;
}

/* Loading and Error Messages */
#loadingIndicator,
#errorMessage {
  position: fixed;
  top: 10px; /* Closer to top */
  right: 10px;
  z-index: 1060; /* Above Bootstrap modals if any */
  min-width: 200px;
}

/* Visualization Sidebar Styles */
.visualization-sidebar {
  position: fixed;
  top: 0;
  right: -450px; /* Start off-screen */
  width: 450px;
  height: 100vh;
  background: #fff;
  box-shadow: -0.5rem 0 1rem rgba(0, 0, 0, 0.15);
  transition: right 0.35s cubic-bezier(0.25, 0.1, 0.25, 1); /* Smoother transition */
  z-index: 1050;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--bs-border-color);
}
.visualization-sidebar.active { right: 0; }

.visualization-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--bs-border-color);
  background: var(--table-header-bg);
}
.visualization-header h5 {
    font-size: 1.1rem;
    font-weight: 600;
}
.visualization-content {
  flex-grow: 1; /* Allow content to take available space */
  padding: 1rem;
  overflow-y: auto; /* Scroll if content is too tall */
}
#visualization { /* Chart canvas */
  max-height: calc(100vh - 150px); /* Prevent chart from being too tall */
}


/* Overlay Styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  z-index: 1040;
  display: none; /* Hidden by default */
  transition: background 0.35s ease;
}
.overlay.active {
  display: block; /* Show when active */
  background: rgba(0, 0, 0, 0.4); /* Slightly less dark */
}

/* Cell Truncation & Expansion (already modified in previous step, ensure it's integrated) */
.expandable-cell {
  display: flex;
  align-items: baseline;
  width: 100%;
  overflow: hidden;
}
.cell-truncated-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline;
  flex-grow: 0;
  flex-shrink: 1;
}
.expand-toggle-btn {
  margin-left: 4px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.85em;
  padding: 0 2px;
  color: var(--bs-primary);
  line-height: 1;
  flex-shrink: 0;
}
.expand-toggle-btn:hover { color: var(--bs-secondary); }

.cell-full {
  display: none;
  position: fixed;
  min-width: 250px; /* Min width for readability */
  max-width: 500px; /* Max width */
  background: #fff;
  border: 1px solid #adb5bd; /* Softer border */
  padding: 12px;
  z-index: 1010;
  white-space: normal;
  word-wrap: break-word;
  border-radius: 0.25rem; /* Bootstrap's default radius */
  box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.1);
  font-size: 0.9rem;
  line-height: 1.5;
}
.cell-full-active { display: block !important; }


/* Table cell content default behavior for truncation */
#userTable td > span:not(.cell-full) {
    display: block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
#userTable td .expandable-cell > span:not(.cell-full) { /* More specific for expandable text */
    display: inline; /* Allow button to be next to it */
}


/* Select All Checkbox in Header */
.select-all-container {
    padding: 0 !important; /* Override table cell padding */
    margin: 0 !important;
}
#selectAllCheckbox.form-check-input {
    margin-left: auto; /* Center it if container is wider */
    margin-right: auto;
    border-color: #888; /* Make it slightly more prominent */
}
#selectAllCheckbox.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25); /* Use Bootstrap's focus */
}

/* Tooltip styling for TH elements */
#userTable thead th[title]:hover::after {
    content: attr(title);
    position: absolute;
    left: 50%;
    transform: translateX(-50%) translateY(100%); /* Position below */
    bottom: -5px; /* Small gap */
    background-color: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: normal; /* Allow tooltip text to wrap */
    width: max-content;
    max-width: 300px; /* Limit tooltip width */
    z-index: 20; /* Above sticky header content */
    text-align: left;
    font-weight: normal; /* Normal weight for tooltip text */
    pointer-events: none; /* Allow clicks to pass through */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
}
#userTable thead th[title]:hover {
    position: relative; /* Needed for absolute positioning of ::after */
}
#userTable thead th[title]:hover::after {
    opacity: 1;
    visibility: visible;
}
