document.addEventListener("DOMContentLoaded", function () {
  document
    .getElementById("loginForm")
    .addEventListener("submit", async function (e) {
      e.preventDefault();

      const userId = document.getElementById("userId").value;
      const password = document.getElementById("password").value;
      const loginButton = this.querySelector('button[type="submit"]'); // Get the button

       // Disable button and show loading state
      loginButton.disabled = true;
      loginButton.textContent = 'Logging in...';


      try {
        const response = await fetch("php/dashboard-login.php", { // Use the correct PHP endpoint
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ userId, password }),
        });

        const data = await response.json(); // Attempt to parse JSON regardless of status

        if (response.ok && data.ok) {
           // Login successful
           // No need to store token in localStorage if using sessions
           window.location.href = "dashboard.php"; // Redirect to the SECURED PHP page
        } else {
           // Login failed - Show error message from server or a default
           alert(data.error || "Invalid credentials");
           // Re-enable button on failure
           loginButton.disabled = false;
           loginButton.textContent = 'Login';
        }
      } catch (error) {
        console.error("Login error:", error);
        alert("An error occurred during login. Check console for details.");
         // Re-enable button on network/parsing error
        loginButton.disabled = false;
        loginButton.textContent = 'Login';
      }
    });
});