-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 20, 2025 at 08:34 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `open_digi_db`
--
CREATE DATABASE IF NOT EXISTS `open_digi_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `open_digi_db`;

-- --------------------------------------------------------

--
-- Table structure for table `dashboard_users`
--

CREATE TABLE `dashboard_users` (
  `id` int(11) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dashboard_users`
--

INSERT INTO `dashboard_users` (`id`, `user_id`, `password`) VALUES
(1, 'opendigi', 'opendigi123');

-- --------------------------------------------------------

--
-- Table structure for table `open_ended_responses`
--

CREATE TABLE `open_ended_responses` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `question_key` varchar(50) NOT NULL,
  `response` text DEFAULT NULL,
  `attempt` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `open_ended_responses`
--

INSERT INTO `open_ended_responses` (`id`, `user_id`, `question_key`, `response`, `attempt`) VALUES
(2, 4, 't1_strategy', 'T1 open ended group A', 1),
(11, 4, 't2_reflection', 'feedback ending survey t2 group a', 2),
(12, 5, 't1_strategy', 'group B t1 survey end', 1),
(13, 5, 't2_course_list', 'kurse group b', 2),
(14, 5, 't2_course_feedback', 'kurse feedback group b', 2),
(15, 5, 't2_reflection', 't2 survey end feedback', 2),
(19, 5, 't3_additional_reflection', 'feedback 2 t3 group b', 3),
(20, 6, 't2_reflection', 'group c t2 survey end response', 2),
(25, 8, 't2_reflection', 't2 end survey grup d', 2),
(26, 8, 't3_reflection', 't3 end group d', 3),
(27, 8, 't3_additional_reflection', 't3 responce group d 2', 3),
(44, 4, 't2_course_list', 'kurse test again for group a t2', 2),
(45, 4, 't2_course_feedback', 'feedback course t2 group a test', 2),
(51, 5, 't3_reflection', 'T3 feedback group B', 3),
(52, 6, 't3_reflection', 't3 group c survey end', 3),
(54, 4, 't3_reflection', 'T3 feedback survey end for group A', 3),
(95, 24, 't1_strategy', 'jdjf jjdjd jdjdjd', 1),
(96, 24, 't2_course_list', 'ghfghhg chcghcg chgchcgh nchcgh', 2),
(97, 24, 't2_course_feedback', 'hfy gfgj hfgfy', 2),
(98, 24, 't2_reflection', 'kjko.huih fghf  hfgh dfrt xf dfxd xdxd xt x', 2),
(99, 24, 't3_reflection', 'njvhhj', 3),
(100, 25, 't1_strategy', 'suchen', 1),
(101, 25, 't2_course_list', 'suchen', 2),
(102, 25, 't2_course_feedback', 'sdfsdfsdf', 2),
(103, 25, 't2_reflection', 'ich fuhle ich viel besser .omg das war so toll sjkdhak jhdkajhffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffssssssssssssssssssssssssssssssssssssssssssssssssssssssssss    ich fuhle ich viel besser .omg das war so toll sjkdhak jhdkajhffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffssssssssssssssssssssssssssssssssssssssssssssssssssssssssss    ich fuhle ich viel besser .omg das war so toll sjkdhak jhdkajhffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffssssssssssssssssssssssssssssssssssssssssssssssssssssssssss    ich fuhle ich viel besser .omg das war so toll sjkdhak jhdkajhffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffssssssssssssssssssssssssssssssssssssssssssssssssssssssssss    ich fuhle ich viel besser .omg das war so toll sjkdhak jhdkajhffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffssssssssssssssssssssssssssssssssssssssssssssssssssssssssss    ich fuhle ich viel besser .omg das war so toll sjkdhak jhdkajhffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffssssssssssssssssssssssssssssssssssssssssssssssssssssssssss', 2),
(106, 26, 't2_reflection', 'kjkkjkkjk', 2),
(108, 27, 't2_reflection', 'hjgugumgu', 2),
(109, 27, 't3_reflection', 'ghgu,gg,ug,y cfg cc ch ccgh ydch cgh cgh gy  ch cgh cghcf fc chcgh cgh cghcgcghch   cgch cgh cgh cgh cg gch gc cgh cg c cgj cgj g ghfghjvhhvjjvjghv   gg h gg', 3),
(110, 26, 't3_reflection', 'huoiyufuhuygyuyuufyjhgfyyt u', 3),
(111, 25, 't3_reflection', 'jhglkfdhgldfk', 3),
(112, 25, 't3_additional_reflection', 'ghgug', 3),
(124, 29, 't1_strategy', 'asasas', 1),
(125, 29, 't2_course_list', 'kurse1', 2),
(126, 29, 't2_course_feedback', 'sehr gut', 2),
(127, 29, 't2_reflection', 'fssafsdaf', 2),
(128, 29, 't3_reflection', 'gjnghjghj', 3),
(129, 30, 't1_strategy', 'test group b t1', 1),
(132, 30, 't2_course_list', 'kurse1 group b', 2),
(133, 30, 't2_course_feedback', 'sehr gut 1', 2),
(134, 30, 't2_reflection', 'this is a feedback gor t2 group b', 2),
(135, 30, 't3_reflection', 'sxzdsf', 3),
(136, 30, 't3_additional_reflection', 'asasasa', 3),
(137, 31, 't2_reflection', 'gftyfyu', 2),
(140, 31, 't3_reflection', 'adadad', 3),
(141, 32, 't2_reflection', 'sasasa', 2),
(142, 32, 't3_reflection', 'grggdfg', 3),
(143, 32, 't3_additional_reflection', 'gsdfgdfgdf', 3);

-- --------------------------------------------------------

--
-- Table structure for table `pre_survey_responses`
--

CREATE TABLE `pre_survey_responses` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `question_id` varchar(50) NOT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `pre_survey_responses`
--

INSERT INTO `pre_survey_responses` (`id`, `user_id`, `question_id`, `response`) VALUES
(7, 4, 'q-2_0', 'Nein'),
(8, 4, 'q-2_1', 'Gruppe A'),
(9, 5, 'q-2_0', 'Ja'),
(10, 5, 'q-2_1', 'Gruppe B'),
(11, 6, 'q-2_0', 'Ja'),
(12, 6, 'q-2_1', 'Gruppe C'),
(15, 8, 'q-2_0', 'Ja'),
(16, 8, 'q-2_1', 'Gruppe D'),
(49, 24, 'q-2_0', 'Ja'),
(50, 24, 'q-2_1', 'Gruppe A'),
(51, 25, 'q-2_0', 'Ja'),
(52, 25, 'q-2_1', 'Gruppe B'),
(53, 26, 'q-2_0', 'Ja'),
(54, 26, 'q-2_1', 'Gruppe C'),
(55, 27, 'q-2_0', 'Ja'),
(56, 27, 'q-2_1', 'Gruppe D'),
(59, 29, 'q-2_0', 'Ja'),
(60, 29, 'q-2_1', 'Gruppe A'),
(61, 30, 'q-2_0', 'Ja'),
(62, 30, 'q-2_1', 'Gruppe B'),
(63, 31, 'q-2_0', 'Ja'),
(64, 31, 'q-2_1', 'Gruppe C'),
(65, 32, 'q-2_0', 'Ja'),
(66, 32, 'q-2_1', 'Gruppe D'),
(67, 33, 'q-2_0', 'Nein'),
(68, 33, 'q-2_1', 'Gruppe A');

-- --------------------------------------------------------

--
-- Table structure for table `responses`
--

CREATE TABLE `responses` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `question_id` varchar(50) NOT NULL,
  `response` text DEFAULT NULL,
  `attempt` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `responses`
--

INSERT INTO `responses` (`id`, `user_id`, `question_id`, `response`, `attempt`) VALUES
(178, 4, 'q-2_0', 'Nein', 1),
(179, 4, 'q0_0', 'Männlich', 1),
(180, 4, 'q0_1', '2001', 1),
(181, 4, 'q0_2', 'Nein', 1),
(182, 4, 'q0_7', '', 1),
(183, 4, 'q0_8', 'ich studiere T1 Group A', 1),
(184, 4, 'q0_3', '1', 1),
(185, 4, 'q0_4', '1.5', 1),
(186, 4, 'q1_0', '5', 1),
(187, 4, 'q1_1', '4', 1),
(188, 4, 'q1_2', '5', 1),
(189, 4, 'q1_3', '4', 1),
(190, 4, 'q1_4', '5', 1),
(191, 4, 'q1_5', '4', 1),
(192, 4, 'q2_0', '3', 1),
(193, 4, 'q2_1', '4', 1),
(194, 4, 'q2_2', '3', 1),
(195, 4, 'q2_3', '4', 1),
(196, 4, 'q2_4', '3', 1),
(197, 4, 'q2_5', '4', 1),
(198, 4, 'q2_6', '3', 1),
(199, 4, 'q3_0', '4', 1),
(200, 4, 'q3_1', '5', 1),
(201, 4, 'q3_2', '4', 1),
(202, 4, 'q3_3', '5', 1),
(203, 4, 'q3_4', '4', 1),
(204, 4, 'q3_5', '5', 1),
(205, 4, 'q3_6', '4', 1),
(206, 4, 'q4_0', '4', 1),
(207, 4, 'q4_1', '5', 1),
(208, 4, 'q4_2', '4', 1),
(209, 4, 'q4_3', '5', 1),
(210, 4, 'q4_4', '4', 1),
(211, 4, 'q4_5', '5', 1),
(212, 4, 'q5_0', '4', 1),
(213, 4, 'q5_1', '5', 1),
(214, 4, 'q5_2', '4', 1),
(215, 4, 'q5_3', '5', 1),
(216, 4, 'q5_4', '4', 1),
(217, 4, 'q5_5', '5', 1),
(218, 4, 'q5_6', '4', 1),
(219, 4, 'q5_7', '5', 1),
(220, 4, 'q5_8', '4', 1),
(221, 4, 'q5_9', '5', 1),
(222, 4, 'q5_10', '4', 1),
(223, 4, 'q6_0', '5', 1),
(224, 4, 'q6_1', '4', 1),
(225, 4, 'q6_2', '5', 1),
(226, 4, 'q6_3', '4', 1),
(227, 4, 'q6_4', '5', 1),
(228, 4, 'q6_5', '4', 1),
(470, 5, 'q-2_0', 'Ja', 1),
(471, 5, 'q-2_1', 'Gruppe B', 1),
(472, 5, 'q0_0', 'Weiblich', 1),
(473, 5, 'q0_1', '1995', 1),
(474, 5, 'q0_2', 'Ja', 1),
(475, 5, 'q0_6', 'Lehramt für Sonderpädagogik', 1),
(476, 5, 'q0_7', 'facher group b', 1),
(477, 5, 'q0_8', '', 1),
(478, 5, 'q0_3', '2', 1),
(479, 5, 'q0_4', '2', 1),
(480, 5, 'q1_0', '4', 1),
(481, 5, 'q1_1', '5', 1),
(482, 5, 'q1_2', '4', 1),
(483, 5, 'q1_3', '5', 1),
(484, 5, 'q1_4', '4', 1),
(485, 5, 'q1_5', '3', 1),
(486, 5, 'q2_0', '4', 1),
(487, 5, 'q2_1', '5', 1),
(488, 5, 'q2_2', '4', 1),
(489, 5, 'q2_3', '3', 1),
(490, 5, 'q2_4', '5', 1),
(491, 5, 'q2_5', '4', 1),
(492, 5, 'q2_6', '5', 1),
(493, 5, 'q3_0', '4', 1),
(494, 5, 'q3_1', '5', 1),
(495, 5, 'q3_2', '4', 1),
(496, 5, 'q3_3', '5', 1),
(497, 5, 'q3_4', '4', 1),
(498, 5, 'q3_5', '3', 1),
(499, 5, 'q3_6', '4', 1),
(500, 5, 'q4_0', '4', 1),
(501, 5, 'q4_1', '5', 1),
(502, 5, 'q4_2', '4', 1),
(503, 5, 'q4_3', '3', 1),
(504, 5, 'q4_4', '4', 1),
(505, 5, 'q4_5', '5', 1),
(506, 5, 'q5_0', '4', 1),
(507, 5, 'q5_1', '5', 1),
(508, 5, 'q5_2', '4', 1),
(509, 5, 'q5_3', '3', 1),
(510, 5, 'q5_4', '4', 1),
(511, 5, 'q5_5', '5', 1),
(512, 5, 'q5_6', '4', 1),
(513, 5, 'q5_7', '5', 1),
(514, 5, 'q5_8', '4', 1),
(515, 5, 'q5_9', '3', 1),
(516, 5, 'q5_10', '4', 1),
(517, 5, 'q6_0', '3', 1),
(518, 5, 'q6_1', '5', 1),
(519, 5, 'q6_2', '4', 1),
(520, 5, 'q6_3', '3', 1),
(521, 5, 'q6_4', '4', 1),
(522, 5, 'q6_5', '5', 1),
(523, 5, 'q0_1', '1995', 2),
(524, 5, 'q0_7', 'facher group b', 2),
(525, 5, 'q0_8', '', 2),
(526, 5, 'q0_3', '2', 2),
(527, 5, 'q0_4', '2', 2),
(528, 5, 'q1_0', '5', 2),
(529, 5, 'q1_1', '4', 2),
(530, 5, 'q1_2', '5', 2),
(531, 5, 'q1_3', '4', 2),
(532, 5, 'q1_4', '5', 2),
(533, 5, 'q1_5', '4', 2),
(534, 5, 'q2_0', '4', 2),
(535, 5, 'q2_1', '5', 2),
(536, 5, 'q2_2', '4', 2),
(537, 5, 'q2_3', '5', 2),
(538, 5, 'q2_4', '4', 2),
(539, 5, 'q2_5', '5', 2),
(540, 5, 'q2_6', '4', 2),
(541, 5, 'q3_0', '4', 2),
(542, 5, 'q3_1', '5', 2),
(543, 5, 'q3_2', '4', 2),
(544, 5, 'q3_3', '5', 2),
(545, 5, 'q3_4', '4', 2),
(546, 5, 'q3_5', '5', 2),
(547, 5, 'q3_6', '4', 2),
(548, 5, 'q4_0', '4', 2),
(549, 5, 'q4_1', '5', 2),
(550, 5, 'q4_2', '4', 2),
(551, 5, 'q4_3', '5', 2),
(552, 5, 'q4_4', '4', 2),
(553, 5, 'q4_5', '5', 2),
(554, 5, 'q5_0', '4', 2),
(555, 5, 'q5_1', '5', 2),
(556, 5, 'q5_2', '4', 2),
(557, 5, 'q5_3', '5', 2),
(558, 5, 'q5_4', '4', 2),
(559, 5, 'q5_5', '5', 2),
(560, 5, 'q5_6', '4', 2),
(561, 5, 'q5_7', '3', 2),
(562, 5, 'q5_8', '4', 2),
(563, 5, 'q5_9', '5', 2),
(564, 5, 'q5_10', '4', 2),
(565, 5, 'q6_0', '4', 2),
(566, 5, 'q6_1', '5', 2),
(567, 5, 'q6_2', '4', 2),
(568, 5, 'q6_3', '5', 2),
(569, 5, 'q6_4', '4', 2),
(570, 5, 'q6_5', '5', 2),
(665, 6, 'q-2_0', 'Ja', 1),
(666, 6, 'q-2_1', 'Gruppe C', 1),
(667, 6, 'q0_0', 'Andere Geschlechtsidentität', 1),
(668, 6, 'q0_1', '1997', 1),
(669, 6, 'q0_2', 'Ja', 1),
(670, 6, 'q0_6', 'Berufspädagogik (M.A.)', 1),
(671, 6, 'q0_7', 'group c facher test', 1),
(672, 6, 'q0_8', '', 1),
(673, 6, 'q0_3', '3', 1),
(674, 6, 'q0_4', '3', 1),
(675, 6, 'q1_0', '4', 1),
(676, 6, 'q1_1', '4', 1),
(677, 6, 'q1_2', '4', 1),
(678, 6, 'q1_3', '4', 1),
(679, 6, 'q1_4', '4', 1),
(680, 6, 'q1_5', '4', 1),
(681, 6, 'q2_0', '4', 1),
(682, 6, 'q2_1', '4', 1),
(683, 6, 'q2_2', '4', 1),
(684, 6, 'q2_3', '4', 1),
(685, 6, 'q2_4', '4', 1),
(686, 6, 'q2_5', '4', 1),
(687, 6, 'q2_6', '4', 1),
(688, 6, 'q3_0', '4', 1),
(689, 6, 'q3_1', '4', 1),
(690, 6, 'q3_2', '4', 1),
(691, 6, 'q3_3', '4', 1),
(692, 6, 'q3_4', '4', 1),
(693, 6, 'q3_5', '4', 1),
(694, 6, 'q3_6', '4', 1),
(695, 6, 'q4_0', '4', 1),
(696, 6, 'q4_1', '4', 1),
(697, 6, 'q4_2', '4', 1),
(698, 6, 'q4_3', '4', 1),
(699, 6, 'q4_4', '4', 1),
(700, 6, 'q4_5', '4', 1),
(701, 6, 'q5_0', '4', 1),
(702, 6, 'q5_1', '4', 1),
(703, 6, 'q5_2', '4', 1),
(704, 6, 'q5_3', '4', 1),
(705, 6, 'q5_4', '4', 1),
(706, 6, 'q5_5', '4', 1),
(707, 6, 'q5_6', '4', 1),
(708, 6, 'q5_7', '4', 1),
(709, 6, 'q5_8', '4', 1),
(710, 6, 'q5_9', '4', 1),
(711, 6, 'q5_10', '4', 1),
(718, 6, 'q6_0', '4', 1),
(719, 6, 'q6_1', '4', 1),
(720, 6, 'q6_2', '4', 1),
(721, 6, 'q6_3', '4', 1),
(722, 6, 'q6_4', '4', 1),
(723, 6, 'q6_5', '4', 1),
(729, 6, 'q1_0', '5', 2),
(730, 6, 'q1_1', '5', 2),
(731, 6, 'q1_2', '5', 2),
(732, 6, 'q1_3', '5', 2),
(733, 6, 'q1_4', '5', 2),
(734, 6, 'q1_5', '5', 2),
(735, 6, 'q2_0', '5', 2),
(736, 6, 'q2_1', '5', 2),
(737, 6, 'q2_2', '5', 2),
(738, 6, 'q2_3', '5', 2),
(739, 6, 'q2_4', '5', 2),
(740, 6, 'q2_5', '5', 2),
(741, 6, 'q2_6', '5', 2),
(742, 6, 'q3_0', '5', 2),
(743, 6, 'q3_1', '5', 2),
(744, 6, 'q3_2', '5', 2),
(745, 6, 'q3_3', '5', 2),
(746, 6, 'q3_4', '5', 2),
(747, 6, 'q3_5', '5', 2),
(748, 6, 'q3_6', '5', 2),
(749, 6, 'q4_0', '5', 2),
(750, 6, 'q4_1', '5', 2),
(751, 6, 'q4_2', '5', 2),
(752, 6, 'q4_3', '5', 2),
(753, 6, 'q4_4', '5', 2),
(754, 6, 'q4_5', '5', 2),
(755, 6, 'q5_0', '5', 2),
(756, 6, 'q5_1', '5', 2),
(757, 6, 'q5_2', '5', 2),
(758, 6, 'q5_3', '5', 2),
(759, 6, 'q5_4', '5', 2),
(760, 6, 'q5_5', '5', 2),
(761, 6, 'q5_6', '5', 2),
(762, 6, 'q5_7', '5', 2),
(763, 6, 'q5_8', '5', 2),
(764, 6, 'q5_9', '5', 2),
(765, 6, 'q5_10', '5', 2),
(766, 6, 'q6_0', '5', 2),
(767, 6, 'q6_1', '5', 2),
(768, 6, 'q6_2', '5', 2),
(769, 6, 'q6_3', '5', 2),
(770, 6, 'q6_4', '5', 2),
(771, 6, 'q6_5', '5', 2),
(772, 6, 'q0_1', '1997', 2),
(773, 6, 'q0_7', 'group c facher test', 2),
(774, 6, 'q0_8', '', 2),
(775, 6, 'q0_3', '3', 2),
(776, 6, 'q0_4', '3', 2),
(826, 8, 'q-2_0', 'Ja', 1),
(827, 8, 'q-2_1', 'Gruppe D', 1),
(828, 8, 'q0_0', 'Weiblich', 1),
(829, 8, 'q0_1', '1996', 1),
(830, 8, 'q0_2', 'Nein', 1),
(831, 8, 'q0_7', '', 1),
(832, 8, 'q0_8', 'dadad', 1),
(833, 8, 'q0_3', '4', 1),
(834, 8, 'q0_4', '4', 1),
(835, 8, 'q1_0', '4', 1),
(836, 8, 'q1_1', '3', 1),
(837, 8, 'q1_2', '2', 1),
(838, 8, 'q1_3', '1', 1),
(839, 8, 'q1_4', '0', 1),
(840, 8, 'q1_5', '1', 1),
(841, 8, 'q2_0', '0', 1),
(842, 8, 'q2_1', '1', 1),
(843, 8, 'q2_2', '2', 1),
(844, 8, 'q2_3', '3', 1),
(845, 8, 'q2_4', '4', 1),
(846, 8, 'q2_5', '5', 1),
(847, 8, 'q2_6', '6', 1),
(848, 8, 'q3_0', '6', 1),
(849, 8, 'q3_1', '5', 1),
(850, 8, 'q3_2', '4', 1),
(851, 8, 'q3_3', '3', 1),
(852, 8, 'q3_4', '2', 1),
(853, 8, 'q3_5', '1', 1),
(854, 8, 'q3_6', '0', 1),
(855, 8, 'q4_0', '0', 1),
(856, 8, 'q4_1', '1', 1),
(857, 8, 'q4_2', '2', 1),
(858, 8, 'q4_3', '3', 1),
(859, 8, 'q4_4', '4', 1),
(860, 8, 'q4_5', '5', 1),
(861, 8, 'q5_0', '6', 1),
(862, 8, 'q5_1', '5', 1),
(863, 8, 'q5_2', '4', 1),
(864, 8, 'q5_3', '3', 1),
(865, 8, 'q5_4', '2', 1),
(866, 8, 'q5_5', '1', 1),
(867, 8, 'q5_6', '0', 1),
(868, 8, 'q5_7', '1', 1),
(869, 8, 'q5_8', '2', 1),
(870, 8, 'q5_9', '3', 1),
(871, 8, 'q5_10', '4', 1),
(878, 8, 'q6_0', '6', 1),
(879, 8, 'q6_1', '5', 1),
(880, 8, 'q6_2', '4', 1),
(881, 8, 'q6_3', '3', 1),
(882, 8, 'q6_4', '2', 1),
(883, 8, 'q6_5', '1', 1),
(931, 8, 'q0_1', '1996', 2),
(932, 8, 'q0_7', '', 2),
(933, 8, 'q0_3', '4', 2),
(934, 8, 'q0_4', '4', 2),
(935, 8, 'q1_0', '5', 2),
(936, 8, 'q1_1', '5', 2),
(937, 8, 'q1_2', '5', 2),
(938, 8, 'q1_3', '5', 2),
(939, 8, 'q1_4', '5', 2),
(940, 8, 'q1_5', '5', 2),
(941, 8, 'q2_0', '5', 2),
(942, 8, 'q2_1', '5', 2),
(943, 8, 'q2_2', '5', 2),
(944, 8, 'q2_3', '5', 2),
(945, 8, 'q2_4', '5', 2),
(946, 8, 'q2_5', '5', 2),
(947, 8, 'q2_6', '5', 2),
(948, 8, 'q3_0', '5', 2),
(949, 8, 'q3_1', '5', 2),
(950, 8, 'q3_2', '5', 2),
(951, 8, 'q3_3', '5', 2),
(952, 8, 'q3_4', '5', 2),
(953, 8, 'q3_5', '5', 2),
(954, 8, 'q3_6', '5', 2),
(955, 8, 'q4_0', '5', 2),
(956, 8, 'q4_1', '5', 2),
(957, 8, 'q4_2', '5', 2),
(958, 8, 'q4_3', '5', 2),
(959, 8, 'q4_4', '5', 2),
(960, 8, 'q4_5', '5', 2),
(961, 8, 'q5_0', '5', 2),
(962, 8, 'q5_1', '5', 2),
(963, 8, 'q5_2', '5', 2),
(964, 8, 'q5_3', '5', 2),
(965, 8, 'q5_4', '5', 2),
(966, 8, 'q5_5', '5', 2),
(967, 8, 'q5_6', '5', 2),
(968, 8, 'q5_7', '5', 2),
(969, 8, 'q5_8', '5', 2),
(970, 8, 'q5_9', '5', 2),
(971, 8, 'q5_10', '5', 2),
(972, 8, 'q6_0', '5', 2),
(973, 8, 'q6_1', '5', 2),
(974, 8, 'q6_2', '5', 2),
(975, 8, 'q6_3', '5', 2),
(976, 8, 'q6_4', '5', 2),
(977, 8, 'q6_5', '5', 2),
(1025, 8, 'q0_1', '1996', 3),
(1026, 8, 'q0_7', '', 3),
(1027, 8, 'q0_3', '4', 3),
(1028, 8, 'q0_4', '4', 3),
(1029, 8, 'q1_0', '6', 3),
(1030, 8, 'q1_1', '6', 3),
(1031, 8, 'q1_2', '6', 3),
(1032, 8, 'q1_3', '6', 3),
(1033, 8, 'q1_4', '6', 3),
(1034, 8, 'q1_5', '6', 3),
(1035, 8, 'q2_0', '6', 3),
(1036, 8, 'q2_1', '6', 3),
(1037, 8, 'q2_2', '6', 3),
(1038, 8, 'q2_3', '6', 3),
(1039, 8, 'q2_4', '6', 3),
(1040, 8, 'q2_5', '6', 3),
(1041, 8, 'q2_6', '6', 3),
(1042, 8, 'q3_0', '6', 3),
(1043, 8, 'q3_1', '6', 3),
(1044, 8, 'q3_2', '6', 3),
(1045, 8, 'q3_3', '6', 3),
(1046, 8, 'q3_4', '6', 3),
(1047, 8, 'q3_5', '6', 3),
(1048, 8, 'q3_6', '6', 3),
(1049, 8, 'q4_0', '6', 3),
(1050, 8, 'q4_1', '6', 3),
(1051, 8, 'q4_2', '6', 3),
(1052, 8, 'q4_3', '6', 3),
(1053, 8, 'q4_4', '6', 3),
(1054, 8, 'q4_5', '6', 3),
(1055, 8, 'q5_0', '6', 3),
(1056, 8, 'q5_1', '6', 3),
(1057, 8, 'q5_2', '6', 3),
(1058, 8, 'q5_3', '6', 3),
(1059, 8, 'q5_4', '6', 3),
(1060, 8, 'q5_5', '6', 3),
(1061, 8, 'q5_6', '6', 3),
(1062, 8, 'q5_7', '6', 3),
(1063, 8, 'q5_8', '6', 3),
(1064, 8, 'q5_9', '6', 3),
(1065, 8, 'q5_10', '6', 3),
(1066, 8, 'q6_0', '6', 3),
(1067, 8, 'q6_1', '6', 3),
(1068, 8, 'q6_2', '6', 3),
(1069, 8, 'q6_3', '6', 3),
(1070, 8, 'q6_4', '6', 3),
(1071, 8, 'q6_5', '6', 3),
(1122, 4, 'q4_0', '4', 2),
(1123, 4, 'q4_1', '5', 2),
(1124, 4, 'q4_2', '4', 2),
(1125, 4, 'q4_3', '5', 2),
(1126, 4, 'q4_4', '4', 2),
(1127, 4, 'q4_5', '5', 2),
(1128, 4, 'q5_0', '2', 2),
(1129, 4, 'q5_1', '2', 2),
(1130, 4, 'q5_2', '3', 2),
(1131, 4, 'q5_3', '2', 2),
(1132, 4, 'q5_4', '3', 2),
(1133, 4, 'q5_5', '2', 2),
(1134, 4, 'q5_6', '3', 2),
(1135, 4, 'q5_7', '2', 2),
(1136, 4, 'q5_8', '3', 2),
(1137, 4, 'q5_9', '3', 2),
(1138, 4, 'q5_10', '2', 2),
(1139, 4, 'q6_0', '3', 2),
(1140, 4, 'q6_1', '4', 2),
(1141, 4, 'q6_2', '5', 2),
(1142, 4, 'q6_3', '4', 2),
(1143, 4, 'q6_4', '3', 2),
(1144, 4, 'q6_5', '4', 2),
(1159, 4, 'q1_0', '5', 2),
(1160, 4, 'q1_1', '4', 2),
(1161, 4, 'q1_2', '5', 2),
(1162, 4, 'q1_3', '4', 2),
(1163, 4, 'q1_4', '5', 2),
(1164, 4, 'q1_5', '4', 2),
(1165, 4, 'q2_0', '4', 2),
(1166, 4, 'q2_1', '5', 2),
(1167, 4, 'q2_2', '4', 2),
(1168, 4, 'q2_3', '5', 2),
(1169, 4, 'q2_4', '4', 2),
(1170, 4, 'q2_5', '5', 2),
(1171, 4, 'q2_6', '4', 2),
(1172, 4, 'q3_0', '4', 2),
(1173, 4, 'q3_1', '5', 2),
(1174, 4, 'q3_2', '4', 2),
(1175, 4, 'q3_3', '5', 2),
(1176, 4, 'q3_4', '4', 2),
(1177, 4, 'q3_5', '5', 2),
(1178, 4, 'q3_6', '4', 2),
(1179, 4, 'q0_1', '2001', 2),
(1180, 4, 'q0_7', '', 2),
(1181, 4, 'q0_3', '1', 2),
(1182, 4, 'q0_4', '1.5', 2),
(1802, 5, 'q0_1', '1995', 3),
(1803, 5, 'q0_8', '', 3),
(1804, 5, 'q0_3', '2', 3),
(1805, 5, 'q0_4', '2', 3),
(1806, 5, 'q1_0', '4', 3),
(1807, 5, 'q1_1', '5', 3),
(1808, 5, 'q1_2', '4', 3),
(1809, 5, 'q1_3', '5', 3),
(1810, 5, 'q1_4', '4', 3),
(1811, 5, 'q1_5', '5', 3),
(1812, 5, 'q2_0', '4', 3),
(1813, 5, 'q2_1', '5', 3),
(1814, 5, 'q2_2', '4', 3),
(1815, 5, 'q2_3', '5', 3),
(1816, 5, 'q2_4', '4', 3),
(1817, 5, 'q2_5', '5', 3),
(1818, 5, 'q2_6', '4', 3),
(1819, 5, 'q3_0', '4', 3),
(1820, 5, 'q3_1', '5', 3),
(1821, 5, 'q3_2', '4', 3),
(1822, 5, 'q3_3', '5', 3),
(1823, 5, 'q3_4', '4', 3),
(1824, 5, 'q3_5', '5', 3),
(1825, 5, 'q3_6', '4', 3),
(1826, 5, 'q4_0', '4', 3),
(1827, 5, 'q4_1', '5', 3),
(1828, 5, 'q4_2', '4', 3),
(1829, 5, 'q4_3', '5', 3),
(1830, 5, 'q4_4', '4', 3),
(1831, 5, 'q4_5', '5', 3),
(1832, 5, 'q5_0', '4', 3),
(1833, 5, 'q5_1', '5', 3),
(1834, 5, 'q5_2', '4', 3),
(1835, 5, 'q5_3', '5', 3),
(1836, 5, 'q5_4', '4', 3),
(1837, 5, 'q5_5', '5', 3),
(1838, 5, 'q5_6', '4', 3),
(1839, 5, 'q5_7', '5', 3),
(1840, 5, 'q5_8', '4', 3),
(1841, 5, 'q5_9', '5', 3),
(1842, 5, 'q5_10', '4', 3),
(1843, 5, 'q6_0', '4', 3),
(1844, 5, 'q6_1', '5', 3),
(1845, 5, 'q6_2', '4', 3),
(1846, 5, 'q6_3', '5', 3),
(1847, 5, 'q6_4', '4', 3),
(1848, 5, 'q6_5', '5', 3),
(1849, 6, 'q0_1', '1997', 3),
(1850, 6, 'q0_8', '', 3),
(1851, 6, 'q0_3', '3', 3),
(1852, 6, 'q0_4', '3', 3),
(1853, 6, 'q1_0', '6', 3),
(1854, 6, 'q1_1', '6', 3),
(1855, 6, 'q1_2', '6', 3),
(1856, 6, 'q1_3', '6', 3),
(1857, 6, 'q1_4', '6', 3),
(1858, 6, 'q1_5', '6', 3),
(1859, 6, 'q2_0', '6', 3),
(1860, 6, 'q2_1', '6', 3),
(1861, 6, 'q2_2', '6', 3),
(1862, 6, 'q2_3', '6', 3),
(1863, 6, 'q2_4', '6', 3),
(1864, 6, 'q2_5', '6', 3),
(1865, 6, 'q2_6', '6', 3),
(1866, 6, 'q3_0', '6', 3),
(1867, 6, 'q3_1', '6', 3),
(1868, 6, 'q3_2', '6', 3),
(1869, 6, 'q3_3', '6', 3),
(1870, 6, 'q3_4', '6', 3),
(1871, 6, 'q3_5', '6', 3),
(1872, 6, 'q3_6', '6', 3),
(1873, 6, 'q4_0', '6', 3),
(1874, 6, 'q4_1', '6', 3),
(1875, 6, 'q4_2', '6', 3),
(1876, 6, 'q4_3', '6', 3),
(1877, 6, 'q4_4', '6', 3),
(1878, 6, 'q4_5', '6', 3),
(1879, 6, 'q5_0', '6', 3),
(1880, 6, 'q5_1', '6', 3),
(1881, 6, 'q5_2', '6', 3),
(1882, 6, 'q5_3', '6', 3),
(1883, 6, 'q5_4', '6', 3),
(1884, 6, 'q5_5', '6', 3),
(1885, 6, 'q5_6', '6', 3),
(1886, 6, 'q5_7', '6', 3),
(1887, 6, 'q5_8', '6', 3),
(1888, 6, 'q5_9', '6', 3),
(1889, 6, 'q5_10', '6', 3),
(1890, 6, 'q6_0', '6', 3),
(1891, 6, 'q6_1', '6', 3),
(1892, 6, 'q6_2', '6', 3),
(1893, 6, 'q6_3', '6', 3),
(1894, 6, 'q6_4', '6', 3),
(1895, 6, 'q6_5', '6', 3),
(1943, 4, 'q0_1', '2001', 3),
(1944, 4, 'q0_7', '', 3),
(1945, 4, 'q0_3', '1', 3),
(1946, 4, 'q0_4', '1.5', 3),
(1947, 4, 'q1_0', '6', 3),
(1948, 4, 'q1_1', '6', 3),
(1949, 4, 'q1_2', '6', 3),
(1950, 4, 'q1_3', '6', 3),
(1951, 4, 'q1_4', '6', 3),
(1952, 4, 'q1_5', '6', 3),
(1953, 4, 'q2_0', '6', 3),
(1954, 4, 'q2_1', '6', 3),
(1955, 4, 'q2_2', '6', 3),
(1956, 4, 'q2_3', '6', 3),
(1957, 4, 'q2_4', '6', 3),
(1958, 4, 'q2_5', '6', 3),
(1959, 4, 'q2_6', '6', 3),
(1960, 4, 'q3_0', '6', 3),
(1961, 4, 'q3_1', '6', 3),
(1962, 4, 'q3_2', '6', 3),
(1963, 4, 'q3_3', '6', 3),
(1964, 4, 'q3_4', '6', 3),
(1965, 4, 'q3_5', '6', 3),
(1966, 4, 'q3_6', '6', 3),
(1967, 4, 'q4_0', '6', 3),
(1968, 4, 'q4_1', '6', 3),
(1969, 4, 'q4_2', '6', 3),
(1970, 4, 'q4_3', '6', 3),
(1971, 4, 'q4_4', '6', 3),
(1972, 4, 'q4_5', '6', 3),
(1973, 4, 'q5_0', '6', 3),
(1974, 4, 'q5_1', '6', 3),
(1975, 4, 'q5_2', '6', 3),
(1976, 4, 'q5_3', '6', 3),
(1977, 4, 'q5_4', '6', 3),
(1978, 4, 'q5_5', '6', 3),
(1979, 4, 'q5_6', '6', 3),
(1980, 4, 'q5_7', '6', 3),
(1981, 4, 'q5_8', '6', 3),
(1982, 4, 'q5_9', '6', 3),
(1983, 4, 'q5_10', '6', 3),
(1990, 4, 'q6_0', '6', 3),
(1991, 4, 'q6_1', '6', 3),
(1992, 4, 'q6_2', '6', 3),
(1993, 4, 'q6_3', '6', 3),
(1994, 4, 'q6_4', '6', 3),
(1995, 4, 'q6_5', '6', 3),
(3585, 24, 'q-2_0', 'Ja', 1),
(3586, 24, 'q-2_1', 'Gruppe A', 1),
(3587, 24, 'q0_0', 'Weiblich', 1),
(3588, 24, 'q0_1', '1999', 1),
(3589, 24, 'q0_2', 'Ja', 1),
(3590, 24, 'q0_6', 'Lehramt an Gymnasien', 1),
(3591, 24, 'q0_7', 'bio', 1),
(3592, 24, 'q0_8', '', 1),
(3593, 24, 'q0_3', '8', 1),
(3594, 24, 'q0_4', '2.3', 1),
(3595, 24, 'q1_0', '2', 1),
(3596, 24, 'q1_1', '2', 1),
(3597, 24, 'q1_2', '2', 1),
(3598, 24, 'q1_3', '2', 1),
(3599, 24, 'q1_4', '2', 1),
(3600, 24, 'q1_5', '2', 1),
(3601, 24, 'q2_0', '2', 1),
(3602, 24, 'q2_1', '2', 1),
(3603, 24, 'q2_2', '2', 1),
(3604, 24, 'q2_3', '2', 1),
(3605, 24, 'q2_4', '2', 1),
(3606, 24, 'q2_5', '2', 1),
(3607, 24, 'q2_6', '2', 1),
(3608, 24, 'q3_0', '2', 1),
(3609, 24, 'q3_1', '2', 1),
(3610, 24, 'q3_2', '2', 1),
(3611, 24, 'q3_3', '2', 1),
(3612, 24, 'q3_4', '2', 1),
(3613, 24, 'q3_5', '2', 1),
(3614, 24, 'q3_6', '2', 1),
(3615, 24, 'q4_0', '2', 1),
(3616, 24, 'q4_1', '2', 1),
(3617, 24, 'q4_2', '2', 1),
(3618, 24, 'q4_3', '2', 1),
(3619, 24, 'q4_4', '2', 1),
(3620, 24, 'q4_5', '2', 1),
(3621, 24, 'q5_0', '2', 1),
(3622, 24, 'q5_1', '2', 1),
(3623, 24, 'q5_2', '2', 1),
(3624, 24, 'q5_3', '2', 1),
(3625, 24, 'q5_4', '2', 1),
(3626, 24, 'q5_5', '2', 1),
(3627, 24, 'q5_6', '2', 1),
(3628, 24, 'q5_7', '2', 1),
(3629, 24, 'q5_8', '2', 1),
(3630, 24, 'q5_9', '2', 1),
(3631, 24, 'q5_10', '2', 1),
(3632, 24, 'q6_0', '2', 1),
(3633, 24, 'q6_1', '2', 1),
(3634, 24, 'q6_2', '2', 1),
(3635, 24, 'q6_3', '2', 1),
(3636, 24, 'q6_4', '2', 1),
(3637, 24, 'q6_5', '2', 1),
(3638, 24, 'q0_1', '1999', 2),
(3639, 24, 'q0_7', 'bio', 2),
(3640, 24, 'q0_8', '', 2),
(3641, 24, 'q0_3', '8', 2),
(3642, 24, 'q0_4', '2.3', 2),
(3643, 24, 'q1_0', '3', 2),
(3644, 24, 'q1_1', '3', 2),
(3645, 24, 'q1_2', '3', 2),
(3646, 24, 'q1_3', '3', 2),
(3647, 24, 'q1_4', '3', 2),
(3648, 24, 'q1_5', '3', 2),
(3649, 24, 'q2_0', '2', 2),
(3650, 24, 'q2_1', '2', 2),
(3651, 24, 'q2_2', '2', 2),
(3652, 24, 'q2_3', '2', 2),
(3653, 24, 'q2_4', '2', 2),
(3654, 24, 'q2_5', '2', 2),
(3655, 24, 'q2_6', '2', 2),
(3656, 24, 'q3_0', '3', 2),
(3657, 24, 'q3_1', '2', 2),
(3658, 24, 'q3_2', '3', 2),
(3659, 24, 'q3_3', '3', 2),
(3660, 24, 'q3_4', '4', 2),
(3661, 24, 'q3_5', '3', 2),
(3662, 24, 'q3_6', '0', 2),
(3663, 24, 'q4_0', '3', 2),
(3664, 24, 'q4_1', '3', 2),
(3665, 24, 'q4_2', '2', 2),
(3666, 24, 'q4_3', '3', 2),
(3667, 24, 'q4_4', '4', 2),
(3668, 24, 'q4_5', '3', 2),
(3669, 24, 'q5_0', '3', 2),
(3670, 24, 'q5_1', '3', 2),
(3671, 24, 'q5_2', '3', 2),
(3672, 24, 'q5_3', '3', 2),
(3673, 24, 'q5_4', '3', 2),
(3674, 24, 'q5_5', '3', 2),
(3675, 24, 'q5_6', '2', 2),
(3676, 24, 'q5_7', '2', 2),
(3677, 24, 'q5_8', '2', 2),
(3678, 24, 'q5_9', '2', 2),
(3679, 24, 'q5_10', '2', 2),
(3680, 24, 'q6_0', '3', 2),
(3681, 24, 'q6_1', '3', 2),
(3682, 24, 'q6_2', '2', 2),
(3683, 24, 'q6_3', '3', 2),
(3684, 24, 'q6_4', '2', 2),
(3685, 24, 'q6_5', '4', 2),
(3686, 24, 'q0_1', '1999', 3),
(3687, 24, 'q0_7', 'bio', 3),
(3688, 24, 'q0_8', '', 3),
(3689, 24, 'q0_3', '8', 3),
(3690, 24, 'q0_4', '2.3', 3),
(3691, 24, 'q1_0', '3', 3),
(3692, 24, 'q1_1', '3', 3),
(3693, 24, 'q1_2', '3', 3),
(3694, 24, 'q1_3', '2', 3),
(3695, 24, 'q1_4', '2', 3),
(3696, 24, 'q1_5', '2', 3),
(3697, 24, 'q2_0', '2', 3),
(3698, 24, 'q2_1', '2', 3),
(3699, 24, 'q2_2', '2', 3),
(3700, 24, 'q2_3', '2', 3),
(3701, 24, 'q2_4', '2', 3),
(3702, 24, 'q2_5', '2', 3),
(3703, 24, 'q2_6', '2', 3),
(3704, 24, 'q3_0', '2', 3),
(3705, 24, 'q3_1', '3', 3),
(3706, 24, 'q3_2', '2', 3),
(3707, 24, 'q3_3', '3', 3),
(3708, 24, 'q3_4', '2', 3),
(3709, 24, 'q3_5', '3', 3),
(3710, 24, 'q3_6', '2', 3),
(3711, 24, 'q4_0', '2', 3),
(3712, 24, 'q4_1', '3', 3),
(3713, 24, 'q4_2', '2', 3),
(3714, 24, 'q4_3', '3', 3),
(3715, 24, 'q4_4', '1', 3),
(3716, 24, 'q4_5', '1', 3),
(3717, 24, 'q5_0', '2', 3),
(3718, 24, 'q5_1', '1', 3),
(3719, 24, 'q5_2', '2', 3),
(3720, 24, 'q5_3', '1', 3),
(3721, 24, 'q5_4', '2', 3),
(3722, 24, 'q5_5', '2', 3),
(3723, 24, 'q5_6', '2', 3),
(3724, 24, 'q5_7', '2', 3),
(3725, 24, 'q5_8', '1', 3),
(3726, 24, 'q5_9', '2', 3),
(3727, 24, 'q5_10', '2', 3),
(3728, 24, 'q6_0', '1', 3),
(3729, 24, 'q6_1', '1', 3),
(3730, 24, 'q6_2', '1', 3),
(3731, 24, 'q6_3', '2', 3),
(3732, 24, 'q6_4', '1', 3),
(3733, 24, 'q6_5', '2', 3),
(3734, 25, 'q-2_0', 'Ja', 1),
(3735, 25, 'q-2_1', 'Gruppe B', 1),
(3736, 25, 'q0_0', 'Männlich', 1),
(3737, 25, 'q0_1', '2016', 1),
(3738, 25, 'q0_2', 'Ja', 1),
(3739, 25, 'q0_6', 'Lehramt an Regionalen Schulen', 1),
(3740, 25, 'q0_7', 'biologie, mathematik', 1),
(3741, 25, 'q0_8', '', 1),
(3742, 25, 'q0_3', '10', 1),
(3743, 25, 'q0_4', '3.3', 1),
(3744, 25, 'q1_0', '0', 1),
(3745, 25, 'q1_1', '0', 1),
(3746, 25, 'q1_2', '0', 1),
(3747, 25, 'q1_3', '0', 1),
(3748, 25, 'q1_4', '0', 1),
(3749, 25, 'q1_5', '0', 1),
(3750, 25, 'q2_0', '0', 1),
(3751, 25, 'q2_1', '0', 1),
(3752, 25, 'q2_2', '0', 1),
(3753, 25, 'q2_3', '0', 1),
(3754, 25, 'q2_4', '1', 1),
(3755, 25, 'q2_5', '0', 1),
(3756, 25, 'q2_6', '1', 1),
(3757, 25, 'q3_0', '0', 1),
(3758, 25, 'q3_1', '0', 1),
(3759, 25, 'q3_2', '0', 1),
(3760, 25, 'q3_3', '0', 1),
(3761, 25, 'q3_4', '0', 1),
(3762, 25, 'q3_5', '0', 1),
(3763, 25, 'q3_6', '0', 1),
(3764, 25, 'q4_0', '0', 1),
(3765, 25, 'q4_1', '0', 1),
(3766, 25, 'q4_2', '0', 1),
(3767, 25, 'q4_3', '0', 1),
(3768, 25, 'q4_4', '0', 1),
(3769, 25, 'q4_5', '0', 1),
(3770, 25, 'q5_0', '0', 1),
(3771, 25, 'q5_1', '0', 1),
(3772, 25, 'q5_2', '0', 1),
(3773, 25, 'q5_3', '0', 1),
(3774, 25, 'q5_4', '0', 1),
(3775, 25, 'q5_5', '0', 1),
(3776, 25, 'q5_6', '0', 1),
(3777, 25, 'q5_7', '0', 1),
(3778, 25, 'q5_8', '0', 1),
(3779, 25, 'q5_9', '0', 1),
(3780, 25, 'q5_10', '0', 1),
(3781, 25, 'q6_0', '0', 1),
(3782, 25, 'q6_1', '1', 1),
(3783, 25, 'q6_2', '1', 1),
(3784, 25, 'q6_3', '1', 1),
(3785, 25, 'q6_4', '0', 1),
(3786, 25, 'q6_5', '1', 1),
(3787, 25, 'q0_1', '2016', 2),
(3788, 25, 'q0_7', 'biologie, mathematik', 2),
(3789, 25, 'q0_8', '', 2),
(3790, 25, 'q0_3', '10', 2),
(3791, 25, 'q0_4', '3.3', 2),
(3792, 25, 'q1_0', '2', 2),
(3793, 25, 'q1_1', '2', 2),
(3794, 25, 'q1_2', '1', 2),
(3795, 25, 'q1_3', '0', 2),
(3796, 25, 'q1_4', '0', 2),
(3797, 25, 'q1_5', '0', 2),
(3798, 25, 'q2_0', '1', 2),
(3799, 25, 'q2_1', '0', 2),
(3800, 25, 'q2_2', '0', 2),
(3801, 25, 'q2_3', '1', 2),
(3802, 25, 'q2_4', '1', 2),
(3803, 25, 'q2_5', '1', 2),
(3804, 25, 'q2_6', '1', 2),
(3805, 25, 'q3_0', '0', 2),
(3806, 25, 'q3_1', '1', 2),
(3807, 25, 'q3_2', '1', 2),
(3808, 25, 'q3_3', '0', 2),
(3809, 25, 'q3_4', '1', 2),
(3810, 25, 'q3_5', '1', 2),
(3811, 25, 'q3_6', '1', 2),
(3812, 25, 'q4_0', '1', 2),
(3813, 25, 'q4_1', '1', 2),
(3814, 25, 'q4_2', '1', 2),
(3815, 25, 'q4_3', '1', 2),
(3816, 25, 'q4_4', '1', 2),
(3817, 25, 'q4_5', '1', 2),
(3818, 25, 'q5_0', '2', 2),
(3819, 25, 'q5_1', '3', 2),
(3820, 25, 'q5_2', '3', 2),
(3821, 25, 'q5_3', '2', 2),
(3822, 25, 'q5_4', '2', 2),
(3823, 25, 'q5_5', '3', 2),
(3824, 25, 'q5_6', '2', 2),
(3825, 25, 'q5_7', '2', 2),
(3826, 25, 'q5_8', '3', 2),
(3827, 25, 'q5_9', '2', 2),
(3828, 25, 'q5_10', '2', 2),
(3829, 25, 'q6_0', '3', 2),
(3830, 25, 'q6_1', '2', 2),
(3831, 25, 'q6_2', '2', 2),
(3832, 25, 'q6_3', '2', 2),
(3833, 25, 'q6_4', '2', 2),
(3834, 25, 'q6_5', '2', 2),
(3883, 26, 'q-2_0', 'Ja', 1),
(3884, 26, 'q-2_1', 'Gruppe C', 1),
(3885, 26, 'q0_0', 'Männlich', 1),
(3886, 26, 'q0_1', '2001', 1),
(3887, 26, 'q0_2', 'Ja', 1),
(3888, 26, 'q0_6', 'Berufspädagogik (B.A.)', 1),
(3889, 26, 'q0_7', 'elektrotechnik', 1),
(3890, 26, 'q0_8', '', 1),
(3891, 26, 'q0_3', '8', 1),
(3892, 26, 'q0_4', '2.5', 1),
(3893, 26, 'q1_0', '2', 1),
(3894, 26, 'q1_1', '1', 1),
(3895, 26, 'q1_2', '2', 1),
(3896, 26, 'q1_3', '2', 1),
(3897, 26, 'q1_4', '2', 1),
(3898, 26, 'q1_5', '2', 1),
(3899, 26, 'q2_0', '0', 1),
(3900, 26, 'q2_1', '0', 1),
(3901, 26, 'q2_2', '0', 1),
(3902, 26, 'q2_3', '0', 1),
(3903, 26, 'q2_4', '1', 1),
(3904, 26, 'q2_5', '1', 1),
(3905, 26, 'q2_6', '1', 1),
(3906, 26, 'q3_0', '1', 1),
(3907, 26, 'q3_1', '2', 1),
(3908, 26, 'q3_2', '1', 1),
(3909, 26, 'q3_3', '2', 1),
(3910, 26, 'q3_4', '1', 1),
(3911, 26, 'q3_5', '2', 1),
(3912, 26, 'q3_6', '1', 1),
(3913, 26, 'q4_0', '2', 1),
(3914, 26, 'q4_1', '2', 1),
(3915, 26, 'q4_2', '1', 1),
(3916, 26, 'q4_3', '2', 1),
(3917, 26, 'q4_4', '3', 1),
(3918, 26, 'q4_5', '2', 1),
(3919, 26, 'q5_0', '2', 1),
(3920, 26, 'q5_1', '1', 1),
(3921, 26, 'q5_2', '2', 1),
(3922, 26, 'q5_3', '1', 1),
(3923, 26, 'q5_4', '2', 1),
(3924, 26, 'q5_5', '2', 1),
(3925, 26, 'q5_6', '2', 1),
(3926, 26, 'q5_7', '2', 1),
(3927, 26, 'q5_8', '2', 1),
(3928, 26, 'q5_9', '1', 1),
(3929, 26, 'q5_10', '2', 1),
(3930, 26, 'q6_0', '2', 1),
(3931, 26, 'q6_1', '1', 1),
(3932, 26, 'q6_2', '2', 1),
(3933, 26, 'q6_3', '3', 1),
(3934, 26, 'q6_4', '3', 1),
(3935, 26, 'q6_5', '3', 1),
(3936, 26, 'q0_1', '2001', 2),
(3937, 26, 'q0_7', 'elektrotechnik', 2),
(3938, 26, 'q0_8', '', 2),
(3939, 26, 'q0_3', '8', 2),
(3940, 26, 'q0_4', '2.5', 2),
(3941, 26, 'q1_0', '3', 2),
(3942, 26, 'q1_1', '1', 2),
(3943, 26, 'q1_2', '2', 2),
(3944, 26, 'q1_3', '3', 2),
(3945, 26, 'q1_4', '2', 2),
(3946, 26, 'q1_5', '3', 2),
(3947, 26, 'q2_0', '2', 2),
(3948, 26, 'q2_1', '1', 2),
(3949, 26, 'q2_2', '2', 2),
(3950, 26, 'q2_3', '2', 2),
(3951, 26, 'q2_4', '1', 2),
(3952, 26, 'q2_5', '2', 2),
(3953, 26, 'q2_6', '2', 2),
(3954, 26, 'q3_0', '3', 2),
(3955, 26, 'q3_1', '2', 2),
(3956, 26, 'q3_2', '2', 2),
(3957, 26, 'q3_3', '2', 2),
(3958, 26, 'q3_4', '2', 2),
(3959, 26, 'q3_5', '2', 2),
(3960, 26, 'q3_6', '2', 2),
(3961, 26, 'q4_0', '2', 2),
(3962, 26, 'q4_1', '2', 2),
(3963, 26, 'q4_2', '2', 2),
(3964, 26, 'q4_3', '2', 2),
(3965, 26, 'q4_4', '1', 2),
(3966, 26, 'q4_5', '2', 2),
(3967, 26, 'q5_0', '3', 2),
(3968, 26, 'q5_1', '2', 2),
(3969, 26, 'q5_2', '2', 2),
(3970, 26, 'q5_3', '2', 2),
(3971, 26, 'q5_4', '1', 2),
(3972, 26, 'q5_5', '2', 2),
(3973, 26, 'q5_6', '3', 2),
(3974, 26, 'q5_7', '2', 2),
(3975, 26, 'q5_8', '2', 2),
(3976, 26, 'q5_9', '2', 2),
(3977, 26, 'q5_10', '2', 2),
(3978, 26, 'q6_0', '2', 2),
(3979, 26, 'q6_1', '1', 2),
(3980, 26, 'q6_2', '2', 2),
(3981, 26, 'q6_3', '1', 2),
(3982, 26, 'q6_4', '2', 2),
(3983, 26, 'q6_5', '2', 2),
(4032, 27, 'q-2_0', 'Ja', 1),
(4033, 27, 'q-2_1', 'Gruppe D', 1),
(4034, 27, 'q0_0', 'Weiblich', 1),
(4035, 27, 'q0_1', '2000', 1),
(4036, 27, 'q0_2', 'Nein', 1),
(4037, 27, 'q0_7', '', 1),
(4038, 27, 'q0_8', 'yyyyy', 1),
(4039, 27, 'q0_3', '6', 1),
(4040, 27, 'q0_4', '4', 1),
(4041, 27, 'q1_0', '0', 1),
(4042, 27, 'q1_1', '0', 1),
(4043, 27, 'q1_2', '0', 1),
(4044, 27, 'q1_3', '0', 1),
(4045, 27, 'q1_4', '0', 1),
(4046, 27, 'q1_5', '0', 1),
(4047, 27, 'q2_0', '1', 1),
(4048, 27, 'q2_1', '0', 1),
(4049, 27, 'q2_2', '0', 1),
(4050, 27, 'q2_3', '1', 1),
(4051, 27, 'q2_4', '0', 1),
(4052, 27, 'q2_5', '1', 1),
(4053, 27, 'q2_6', '1', 1),
(4054, 27, 'q3_0', '1', 1),
(4055, 27, 'q3_1', '1', 1),
(4056, 27, 'q3_2', '0', 1),
(4057, 27, 'q3_3', '1', 1),
(4058, 27, 'q3_4', '1', 1),
(4059, 27, 'q3_5', '2', 1),
(4060, 27, 'q3_6', '2', 1),
(4061, 27, 'q4_0', '2', 1),
(4062, 27, 'q4_1', '3', 1),
(4063, 27, 'q4_2', '3', 1),
(4064, 27, 'q4_3', '0', 1),
(4065, 27, 'q4_4', '0', 1),
(4066, 27, 'q4_5', '2', 1),
(4067, 27, 'q5_0', '2', 1),
(4068, 27, 'q5_1', '1', 1),
(4069, 27, 'q5_2', '1', 1),
(4070, 27, 'q5_3', '1', 1),
(4071, 27, 'q5_4', '2', 1),
(4072, 27, 'q5_5', '2', 1),
(4073, 27, 'q5_6', '1', 1),
(4074, 27, 'q5_7', '3', 1),
(4075, 27, 'q5_8', '2', 1),
(4076, 27, 'q5_9', '4', 1),
(4077, 27, 'q5_10', '3', 1),
(4078, 27, 'q6_0', '3', 1),
(4079, 27, 'q6_1', '2', 1),
(4080, 27, 'q6_2', '4', 1),
(4081, 27, 'q6_3', '4', 1),
(4082, 27, 'q6_4', '3', 1),
(4083, 27, 'q6_5', '2', 1),
(4084, 27, 'q0_1', '2000', 2),
(4085, 27, 'q0_7', '', 2),
(4086, 27, 'q0_8', 'yyyyy', 2),
(4087, 27, 'q0_3', '6', 2),
(4088, 27, 'q0_4', '4', 2),
(4089, 27, 'q1_0', '3', 2),
(4090, 27, 'q1_1', '5', 2),
(4091, 27, 'q1_2', '3', 2),
(4092, 27, 'q1_3', '4', 2),
(4093, 27, 'q1_4', '4', 2),
(4094, 27, 'q1_5', '3', 2),
(4095, 27, 'q2_0', '5', 2),
(4096, 27, 'q2_1', '4', 2),
(4097, 27, 'q2_2', '4', 2),
(4098, 27, 'q2_3', '5', 2),
(4099, 27, 'q2_4', '5', 2),
(4100, 27, 'q2_5', '4', 2),
(4101, 27, 'q2_6', '5', 2),
(4102, 27, 'q3_0', '5', 2),
(4103, 27, 'q3_1', '5', 2),
(4104, 27, 'q3_2', '4', 2),
(4105, 27, 'q3_3', '5', 2),
(4106, 27, 'q3_4', '4', 2),
(4107, 27, 'q3_5', '5', 2),
(4108, 27, 'q3_6', '5', 2),
(4109, 27, 'q4_0', '5', 2),
(4110, 27, 'q4_1', '4', 2),
(4111, 27, 'q4_2', '5', 2),
(4112, 27, 'q4_3', '1', 2),
(4113, 27, 'q4_4', '2', 2),
(4114, 27, 'q4_5', '2', 2),
(4115, 27, 'q5_0', '2', 2),
(4116, 27, 'q5_1', '1', 2),
(4117, 27, 'q5_2', '1', 2),
(4118, 27, 'q5_3', '0', 2),
(4119, 27, 'q5_4', '1', 2),
(4120, 27, 'q5_5', '0', 2),
(4121, 27, 'q5_6', '3', 2),
(4122, 27, 'q5_7', '1', 2),
(4123, 27, 'q5_8', '1', 2),
(4124, 27, 'q5_9', '2', 2),
(4125, 27, 'q5_10', '3', 2),
(4126, 27, 'q6_0', '3', 2),
(4127, 27, 'q6_1', '2', 2),
(4128, 27, 'q6_2', '3', 2),
(4129, 27, 'q6_3', '1', 2),
(4130, 27, 'q6_4', '3', 2),
(4131, 27, 'q6_5', '2', 2),
(4132, 27, 'q0_1', '2000', 3),
(4133, 27, 'q0_7', '', 3),
(4134, 27, 'q0_8', 'yyyyy', 3),
(4135, 27, 'q0_3', '6', 3),
(4136, 27, 'q0_4', '4', 3),
(4137, 27, 'q1_0', '3', 3),
(4138, 27, 'q1_1', '4', 3),
(4139, 27, 'q1_2', '3', 3),
(4140, 27, 'q1_3', '2', 3),
(4141, 27, 'q1_4', '3', 3),
(4142, 27, 'q1_5', '4', 3),
(4143, 27, 'q2_0', '3', 3),
(4144, 27, 'q2_1', '2', 3),
(4145, 27, 'q2_2', '2', 3),
(4146, 27, 'q2_3', '2', 3),
(4147, 27, 'q2_4', '4', 3),
(4148, 27, 'q2_5', '4', 3),
(4149, 27, 'q2_6', '4', 3),
(4150, 27, 'q3_0', '2', 3),
(4151, 27, 'q3_1', '2', 3),
(4152, 27, 'q3_2', '2', 3),
(4153, 27, 'q3_3', '1', 3),
(4154, 27, 'q3_4', '3', 3),
(4155, 27, 'q3_5', '2', 3),
(4156, 27, 'q3_6', '2', 3),
(4157, 27, 'q4_0', '4', 3),
(4158, 27, 'q4_1', '3', 3),
(4159, 27, 'q4_2', '2', 3),
(4160, 27, 'q4_3', '2', 3),
(4161, 27, 'q4_4', '2', 3),
(4162, 27, 'q4_5', '2', 3),
(4163, 27, 'q5_0', '4', 3),
(4164, 27, 'q5_1', '3', 3),
(4165, 27, 'q5_2', '2', 3),
(4166, 27, 'q5_3', '2', 3),
(4167, 27, 'q5_4', '3', 3),
(4168, 27, 'q5_5', '3', 3),
(4169, 27, 'q5_6', '2', 3),
(4170, 27, 'q5_7', '3', 3),
(4171, 27, 'q5_8', '3', 3),
(4172, 27, 'q5_9', '2', 3),
(4173, 27, 'q5_10', '2', 3),
(4174, 27, 'q6_0', '4', 3),
(4175, 27, 'q6_1', '3', 3),
(4176, 27, 'q6_2', '2', 3),
(4177, 27, 'q6_3', '3', 3),
(4178, 27, 'q6_4', '3', 3),
(4179, 27, 'q6_5', '2', 3),
(4180, 26, 'q0_1', '2001', 3),
(4181, 26, 'q0_7', 'elektrotechnik', 3),
(4182, 26, 'q0_8', '', 3),
(4183, 26, 'q0_3', '8', 3),
(4184, 26, 'q0_4', '2.5', 3),
(4185, 26, 'q1_0', '2', 3),
(4186, 26, 'q1_1', '3', 3),
(4187, 26, 'q1_2', '3', 3),
(4188, 26, 'q1_3', '3', 3),
(4189, 26, 'q1_4', '3', 3),
(4190, 26, 'q1_5', '3', 3),
(4191, 26, 'q2_0', '2', 3),
(4192, 26, 'q2_1', '2', 3),
(4193, 26, 'q2_2', '2', 3),
(4194, 26, 'q2_3', '2', 3),
(4195, 26, 'q2_4', '2', 3),
(4196, 26, 'q2_5', '2', 3),
(4197, 26, 'q2_6', '2', 3),
(4198, 26, 'q3_0', '3', 3),
(4199, 26, 'q3_1', '3', 3),
(4200, 26, 'q3_2', '4', 3),
(4201, 26, 'q3_3', '4', 3),
(4202, 26, 'q3_4', '4', 3),
(4203, 26, 'q3_5', '4', 3),
(4204, 26, 'q3_6', '3', 3),
(4205, 26, 'q4_0', '3', 3),
(4206, 26, 'q4_1', '3', 3),
(4207, 26, 'q4_2', '2', 3),
(4208, 26, 'q4_3', '3', 3),
(4209, 26, 'q4_4', '2', 3),
(4210, 26, 'q4_5', '2', 3),
(4211, 26, 'q5_0', '2', 3),
(4212, 26, 'q5_1', '2', 3),
(4213, 26, 'q5_2', '4', 3),
(4214, 26, 'q5_3', '3', 3),
(4215, 26, 'q5_4', '3', 3),
(4216, 26, 'q5_5', '3', 3),
(4217, 26, 'q5_6', '2', 3),
(4218, 26, 'q5_7', '4', 3),
(4219, 26, 'q5_8', '4', 3),
(4220, 26, 'q5_9', '3', 3),
(4221, 26, 'q5_10', '3', 3),
(4222, 26, 'q6_0', '3', 3),
(4223, 26, 'q6_1', '2', 3),
(4224, 26, 'q6_2', '3', 3),
(4225, 26, 'q6_3', '2', 3),
(4226, 26, 'q6_4', '3', 3),
(4227, 26, 'q6_5', '2', 3),
(4228, 25, 'q0_1', '2016', 3),
(4229, 25, 'q0_7', 'biologie, mathematik', 3),
(4230, 25, 'q0_8', '', 3),
(4231, 25, 'q0_3', '10', 3),
(4232, 25, 'q0_4', '3.3', 3),
(4233, 25, 'q1_0', '2', 3),
(4234, 25, 'q1_1', '1', 3),
(4235, 25, 'q1_2', '2', 3),
(4236, 25, 'q1_3', '2', 3),
(4237, 25, 'q1_4', '3', 3),
(4238, 25, 'q1_5', '2', 3),
(4239, 25, 'q2_0', '3', 3),
(4240, 25, 'q2_1', '2', 3),
(4241, 25, 'q2_2', '3', 3),
(4242, 25, 'q2_3', '3', 3),
(4243, 25, 'q2_4', '3', 3),
(4244, 25, 'q2_5', '3', 3),
(4245, 25, 'q2_6', '3', 3),
(4246, 25, 'q3_0', '3', 3),
(4247, 25, 'q3_1', '3', 3),
(4248, 25, 'q3_2', '3', 3),
(4249, 25, 'q3_3', '3', 3),
(4250, 25, 'q3_4', '2', 3),
(4251, 25, 'q3_5', '2', 3),
(4252, 25, 'q3_6', '2', 3),
(4253, 25, 'q4_0', '3', 3),
(4254, 25, 'q4_1', '1', 3),
(4255, 25, 'q4_2', '1', 3),
(4256, 25, 'q4_3', '1', 3),
(4257, 25, 'q4_4', '1', 3),
(4258, 25, 'q4_5', '1', 3),
(4259, 25, 'q5_0', '3', 3),
(4260, 25, 'q5_1', '1', 3),
(4261, 25, 'q5_2', '2', 3),
(4262, 25, 'q5_3', '1', 3),
(4263, 25, 'q5_4', '2', 3),
(4264, 25, 'q5_5', '2', 3),
(4265, 25, 'q5_6', '4', 3),
(4266, 25, 'q5_7', '3', 3),
(4267, 25, 'q5_8', '6', 3),
(4268, 25, 'q5_9', '6', 3),
(4269, 25, 'q5_10', '6', 3),
(4270, 25, 'q6_0', '6', 3),
(4271, 25, 'q6_1', '6', 3),
(4272, 25, 'q6_2', '6', 3),
(4273, 25, 'q6_3', '6', 3),
(4274, 25, 'q6_4', '6', 3),
(4275, 25, 'q6_5', '6', 3),
(4278, 29, 'q-2_0', 'Ja', 1),
(4279, 29, 'q-2_1', 'Gruppe A', 1),
(4288, 29, 'q0_0', 'Andere Geschlechtsidentität', 1),
(4289, 29, 'q0_1', '2006', 1),
(4290, 29, 'q0_2', 'Ja', 1),
(4291, 29, 'q0_6', 'Lehramt für Sonderpädagogik', 1),
(4292, 29, 'q0_7', 'weeae', 1),
(4293, 29, 'q0_8', '', 1),
(4294, 29, 'q0_3', '1', 1),
(4295, 29, 'q0_4', '0.5', 1),
(4296, 29, 'q1_0', '4', 1),
(4297, 29, 'q1_1', '5', 1),
(4298, 29, 'q1_2', '4', 1),
(4299, 29, 'q1_3', '3', 1),
(4300, 29, 'q1_4', '4', 1),
(4301, 29, 'q1_5', '3', 1),
(4302, 29, 'q2_0', '3', 1),
(4303, 29, 'q2_1', '4', 1),
(4304, 29, 'q2_2', '3', 1),
(4305, 29, 'q2_3', '4', 1),
(4306, 29, 'q2_4', '3', 1),
(4307, 29, 'q2_5', '4', 1),
(4308, 29, 'q2_6', '3', 1),
(4309, 29, 'q3_0', '3', 1),
(4310, 29, 'q3_1', '4', 1),
(4311, 29, 'q3_2', '3', 1),
(4312, 29, 'q3_3', '4', 1),
(4313, 29, 'q3_4', '3', 1),
(4314, 29, 'q3_5', '4', 1),
(4315, 29, 'q3_6', '3', 1),
(4316, 29, 'q4_0', '3', 1),
(4317, 29, 'q4_1', '4', 1),
(4318, 29, 'q4_2', '3', 1),
(4319, 29, 'q4_3', '4', 1),
(4320, 29, 'q4_4', '3', 1),
(4321, 29, 'q4_5', '4', 1),
(4322, 29, 'q5_0', '3', 1),
(4323, 29, 'q5_1', '4', 1),
(4324, 29, 'q5_2', '3', 1),
(4325, 29, 'q5_3', '4', 1),
(4326, 29, 'q5_4', '3', 1),
(4327, 29, 'q5_5', '4', 1),
(4328, 29, 'q5_6', '3', 1),
(4329, 29, 'q5_7', '4', 1),
(4330, 29, 'q5_8', '3', 1),
(4331, 29, 'q5_9', '4', 1),
(4332, 29, 'q5_10', '3', 1),
(4405, 29, 'q6_0', '3', 1),
(4406, 29, 'q6_1', '4', 1),
(4407, 29, 'q6_2', '3', 1),
(4408, 29, 'q6_3', '4', 1),
(4409, 29, 'q6_4', '3', 1),
(4410, 29, 'q6_5', '4', 1),
(4411, 29, 'q0_1', '2006', 2),
(4412, 29, 'q0_7', 'weeae', 2),
(4413, 29, 'q0_8', '', 2),
(4414, 29, 'q0_3', '1', 2),
(4415, 29, 'q0_4', '0.5', 2),
(4416, 29, 'q1_0', '4', 2),
(4417, 29, 'q1_1', '3', 2),
(4418, 29, 'q1_2', '4', 2),
(4419, 29, 'q1_3', '3', 2),
(4420, 29, 'q1_4', '4', 2),
(4421, 29, 'q1_5', '3', 2),
(4422, 29, 'q2_0', '3', 2),
(4423, 29, 'q2_1', '4', 2),
(4424, 29, 'q2_2', '3', 2),
(4425, 29, 'q2_3', '4', 2),
(4426, 29, 'q2_4', '3', 2),
(4427, 29, 'q2_5', '4', 2),
(4428, 29, 'q2_6', '3', 2),
(4429, 29, 'q3_0', '3', 2),
(4430, 29, 'q3_1', '4', 2),
(4431, 29, 'q3_2', '3', 2),
(4432, 29, 'q3_3', '4', 2),
(4433, 29, 'q3_4', '3', 2),
(4434, 29, 'q3_5', '4', 2),
(4435, 29, 'q3_6', '3', 2),
(4436, 29, 'q4_0', '3', 2),
(4437, 29, 'q4_1', '4', 2),
(4438, 29, 'q4_2', '3', 2),
(4439, 29, 'q4_3', '4', 2),
(4440, 29, 'q4_4', '3', 2),
(4441, 29, 'q4_5', '4', 2),
(4442, 29, 'q5_0', '3', 2),
(4443, 29, 'q5_1', '4', 2),
(4444, 29, 'q5_2', '3', 2),
(4445, 29, 'q5_3', '4', 2),
(4446, 29, 'q5_4', '3', 2),
(4447, 29, 'q5_5', '4', 2),
(4448, 29, 'q5_6', '3', 2),
(4449, 29, 'q5_7', '4', 2),
(4450, 29, 'q5_8', '4', 2),
(4451, 29, 'q5_9', '3', 2),
(4452, 29, 'q5_10', '4', 2),
(4453, 29, 'q6_0', '3', 2),
(4454, 29, 'q6_1', '4', 2),
(4455, 29, 'q6_2', '3', 2),
(4456, 29, 'q6_3', '4', 2),
(4457, 29, 'q6_4', '3', 2),
(4458, 29, 'q6_5', '4', 2),
(4459, 29, 'q0_1', '2006', 3),
(4460, 29, 'q0_7', 'weeae', 3),
(4461, 29, 'q0_8', '', 3),
(4462, 29, 'q0_3', '1', 3),
(4463, 29, 'q0_4', '0.5', 3),
(4464, 29, 'q1_0', '4', 3),
(4465, 29, 'q1_1', '5', 3),
(4466, 29, 'q1_2', '4', 3),
(4467, 29, 'q1_3', '3', 3),
(4468, 29, 'q1_4', '4', 3),
(4469, 29, 'q1_5', '5', 3),
(4470, 29, 'q2_0', '4', 3),
(4471, 29, 'q2_1', '5', 3),
(4472, 29, 'q2_2', '4', 3),
(4473, 29, 'q2_3', '3', 3),
(4474, 29, 'q2_4', '4', 3),
(4475, 29, 'q2_5', '5', 3),
(4476, 29, 'q2_6', '4', 3),
(4477, 29, 'q3_0', '4', 3),
(4478, 29, 'q3_1', '5', 3),
(4479, 29, 'q3_2', '4', 3),
(4480, 29, 'q3_3', '5', 3),
(4481, 29, 'q3_4', '4', 3),
(4482, 29, 'q3_5', '4', 3),
(4483, 29, 'q3_6', '5', 3),
(4484, 29, 'q4_0', '4', 3),
(4485, 29, 'q4_1', '4', 3),
(4486, 29, 'q4_2', '5', 3),
(4487, 29, 'q4_3', '4', 3),
(4488, 29, 'q4_4', '4', 3),
(4489, 29, 'q4_5', '5', 3),
(4490, 29, 'q5_0', '4', 3),
(4491, 29, 'q5_1', '5', 3),
(4492, 29, 'q5_2', '4', 3),
(4493, 29, 'q5_3', '3', 3),
(4494, 29, 'q5_4', '5', 3),
(4495, 29, 'q5_5', '4', 3),
(4496, 29, 'q5_6', '5', 3),
(4497, 29, 'q5_7', '4', 3),
(4498, 29, 'q5_8', '3', 3),
(4499, 29, 'q5_9', '4', 3),
(4500, 29, 'q5_10', '5', 3),
(4501, 29, 'q6_0', '4', 3),
(4502, 29, 'q6_1', '5', 3),
(4503, 29, 'q6_2', '4', 3),
(4504, 29, 'q6_3', '3', 3),
(4505, 29, 'q6_4', '4', 3),
(4506, 29, 'q6_5', '5', 3),
(4507, 30, 'q-2_0', 'Ja', 1),
(4508, 30, 'q-2_1', 'Gruppe B', 1),
(4509, 30, 'q0_0', 'Männlich', 1),
(4510, 30, 'q0_1', '2001', 1),
(4511, 30, 'q0_2', 'Ja', 1),
(4512, 30, 'q0_6', 'Berufspädagogik (M.A.)', 1),
(4513, 30, 'q0_7', 'sasada', 1),
(4514, 30, 'q0_8', '', 1),
(4515, 30, 'q0_3', '1', 1),
(4516, 30, 'q0_4', '1', 1),
(4517, 30, 'q1_0', '3', 1),
(4518, 30, 'q1_1', '4', 1),
(4519, 30, 'q1_2', '3', 1),
(4520, 30, 'q1_3', '4', 1),
(4521, 30, 'q1_4', '3', 1),
(4522, 30, 'q1_5', '4', 1),
(4523, 30, 'q2_0', '3', 1),
(4524, 30, 'q2_1', '4', 1),
(4525, 30, 'q2_2', '2', 1),
(4526, 30, 'q2_3', '3', 1),
(4527, 30, 'q2_4', '4', 1),
(4528, 30, 'q2_5', '3', 1),
(4529, 30, 'q2_6', '4', 1),
(4530, 30, 'q3_0', '3', 1),
(4531, 30, 'q3_1', '4', 1),
(4532, 30, 'q3_2', '3', 1),
(4533, 30, 'q3_3', '4', 1),
(4534, 30, 'q3_4', '3', 1),
(4535, 30, 'q3_5', '4', 1),
(4536, 30, 'q3_6', '3', 1),
(4537, 30, 'q4_0', '4', 1),
(4538, 30, 'q4_1', '5', 1),
(4539, 30, 'q4_2', '4', 1),
(4540, 30, 'q4_3', '3', 1),
(4541, 30, 'q4_4', '4', 1),
(4542, 30, 'q4_5', '3', 1),
(4543, 30, 'q5_0', '3', 1),
(4544, 30, 'q5_1', '5', 1),
(4545, 30, 'q5_2', '4', 1),
(4546, 30, 'q5_3', '3', 1),
(4547, 30, 'q5_4', '4', 1),
(4548, 30, 'q5_5', '3', 1),
(4549, 30, 'q5_6', '4', 1),
(4550, 30, 'q5_7', '3', 1),
(4551, 30, 'q5_8', '4', 1),
(4552, 30, 'q5_9', '3', 1),
(4553, 30, 'q5_10', '4', 1),
(4554, 30, 'q6_0', '4', 1),
(4555, 30, 'q6_1', '3', 1),
(4556, 30, 'q6_2', '4', 1),
(4557, 30, 'q6_3', '3', 1),
(4558, 30, 'q6_4', '4', 1),
(4559, 30, 'q6_5', '3', 1),
(4608, 30, 'q0_1', '2001', 2),
(4609, 30, 'q0_7', 'sasada', 2),
(4610, 30, 'q0_8', '', 2),
(4611, 30, 'q0_3', '1', 2),
(4612, 30, 'q0_4', '1', 2),
(4613, 30, 'q1_0', '3', 2),
(4614, 30, 'q1_1', '4', 2),
(4615, 30, 'q1_2', '3', 2),
(4616, 30, 'q1_3', '5', 2),
(4617, 30, 'q1_4', '4', 2),
(4618, 30, 'q1_5', '3', 2),
(4619, 30, 'q2_0', '3', 2),
(4620, 30, 'q2_1', '5', 2),
(4621, 30, 'q2_2', '4', 2),
(4622, 30, 'q2_3', '3', 2),
(4623, 30, 'q2_4', '3', 2),
(4624, 30, 'q2_5', '4', 2),
(4625, 30, 'q2_6', '5', 2),
(4626, 30, 'q3_0', '3', 2),
(4627, 30, 'q3_1', '4', 2),
(4628, 30, 'q3_2', '5', 2),
(4629, 30, 'q3_3', '4', 2),
(4630, 30, 'q3_4', '3', 2),
(4631, 30, 'q3_5', '2', 2),
(4632, 30, 'q3_6', '3', 2),
(4633, 30, 'q4_0', '3', 2),
(4634, 30, 'q4_1', '4', 2),
(4635, 30, 'q4_2', '5', 2),
(4636, 30, 'q4_3', '4', 2),
(4637, 30, 'q4_4', '3', 2),
(4638, 30, 'q4_5', '4', 2),
(4639, 30, 'q5_0', '3', 2),
(4640, 30, 'q5_1', '4', 2),
(4641, 30, 'q5_2', '3', 2),
(4642, 30, 'q5_3', '4', 2),
(4643, 30, 'q5_4', '3', 2),
(4644, 30, 'q5_5', '4', 2),
(4645, 30, 'q5_6', '5', 2),
(4646, 30, 'q5_7', '4', 2),
(4647, 30, 'q5_8', '3', 2),
(4648, 30, 'q5_9', '4', 2),
(4649, 30, 'q5_10', '3', 2),
(4650, 30, 'q6_0', '4', 2),
(4651, 30, 'q6_1', '3', 2),
(4652, 30, 'q6_2', '4', 2),
(4653, 30, 'q6_3', '5', 2),
(4654, 30, 'q6_4', '4', 2),
(4655, 30, 'q6_5', '3', 2),
(4752, 30, 'q0_1', '2001', 3),
(4753, 30, 'q0_7', 'sasada', 3),
(4754, 30, 'q0_8', '', 3),
(4755, 30, 'q0_3', '1', 3),
(4756, 30, 'q0_4', '1', 3),
(4757, 30, 'q1_0', '4', 3),
(4758, 30, 'q1_1', '5', 3),
(4759, 30, 'q1_2', '4', 3),
(4760, 30, 'q1_3', '5', 3),
(4761, 30, 'q1_4', '4', 3),
(4762, 30, 'q1_5', '5', 3),
(4763, 30, 'q2_0', '4', 3),
(4764, 30, 'q2_1', '5', 3),
(4765, 30, 'q2_2', '4', 3),
(4766, 30, 'q2_3', '4', 3),
(4767, 30, 'q2_4', '5', 3),
(4768, 30, 'q2_5', '4', 3),
(4769, 30, 'q2_6', '5', 3),
(4770, 30, 'q3_0', '4', 3),
(4771, 30, 'q3_1', '5', 3),
(4772, 30, 'q3_2', '4', 3),
(4773, 30, 'q3_3', '5', 3),
(4774, 30, 'q3_4', '4', 3),
(4775, 30, 'q3_5', '3', 3),
(4776, 30, 'q3_6', '4', 3),
(4777, 30, 'q4_0', '4', 3),
(4778, 30, 'q4_1', '3', 3),
(4779, 30, 'q4_2', '4', 3),
(4780, 30, 'q4_3', '3', 3),
(4781, 30, 'q4_4', '4', 3),
(4782, 30, 'q4_5', '4', 3),
(4783, 30, 'q5_0', '3', 3),
(4784, 30, 'q5_1', '4', 3),
(4785, 30, 'q5_2', '5', 3),
(4786, 30, 'q5_3', '4', 3),
(4787, 30, 'q5_4', '3', 3),
(4788, 30, 'q5_5', '4', 3),
(4789, 30, 'q5_6', '4', 3),
(4790, 30, 'q5_7', '4', 3),
(4791, 30, 'q5_8', '3', 3),
(4792, 30, 'q5_9', '4', 3),
(4793, 30, 'q5_10', '4', 3),
(4794, 30, 'q6_0', '4', 3),
(4795, 30, 'q6_1', '4', 3),
(4796, 30, 'q6_2', '4', 3),
(4797, 30, 'q6_3', '5', 3),
(4798, 30, 'q6_4', '4', 3),
(4799, 30, 'q6_5', '3', 3),
(4800, 31, 'q-2_0', 'Ja', 1),
(4801, 31, 'q-2_1', 'Gruppe C', 1),
(4802, 31, 'q0_0', 'Andere Geschlechtsidentität', 1),
(4803, 31, 'q0_1', '2002', 1),
(4804, 31, 'q0_2', 'Nein', 1),
(4805, 31, 'q0_7', '', 1),
(4806, 31, 'q0_8', 'sasas', 1),
(4807, 31, 'q0_3', '2', 1),
(4808, 31, 'q0_4', '2', 1),
(4809, 31, 'q1_0', '4', 1),
(4810, 31, 'q1_1', '3', 1),
(4811, 31, 'q1_2', '4', 1),
(4812, 31, 'q1_3', '3', 1),
(4813, 31, 'q1_4', '4', 1),
(4814, 31, 'q1_5', '3', 1),
(4815, 31, 'q2_0', '4', 1),
(4816, 31, 'q2_1', '3', 1),
(4817, 31, 'q2_2', '4', 1),
(4818, 31, 'q2_3', '3', 1),
(4819, 31, 'q2_4', '4', 1),
(4820, 31, 'q2_5', '3', 1),
(4821, 31, 'q2_6', '4', 1),
(4822, 31, 'q3_0', '4', 1),
(4823, 31, 'q3_1', '5', 1),
(4824, 31, 'q3_2', '4', 1),
(4825, 31, 'q3_3', '3', 1),
(4826, 31, 'q3_4', '4', 1),
(4827, 31, 'q3_5', '5', 1),
(4828, 31, 'q3_6', '4', 1),
(4829, 31, 'q4_0', '4', 1),
(4830, 31, 'q4_1', '5', 1),
(4831, 31, 'q4_2', '4', 1),
(4832, 31, 'q4_3', '3', 1),
(4833, 31, 'q4_4', '4', 1),
(4834, 31, 'q4_5', '5', 1),
(4835, 31, 'q5_0', '4', 1),
(4836, 31, 'q5_1', '5', 1),
(4837, 31, 'q5_2', '4', 1),
(4838, 31, 'q5_3', '3', 1),
(4839, 31, 'q5_4', '4', 1),
(4840, 31, 'q5_5', '5', 1),
(4841, 31, 'q5_6', '4', 1),
(4842, 31, 'q5_7', '3', 1),
(4843, 31, 'q5_8', '4', 1),
(4844, 31, 'q5_9', '5', 1),
(4845, 31, 'q5_10', '4', 1),
(4846, 31, 'q6_0', '4', 1),
(4847, 31, 'q6_1', '5', 1),
(4848, 31, 'q6_2', '4', 1),
(4849, 31, 'q6_3', '3', 1),
(4850, 31, 'q6_4', '4', 1),
(4851, 31, 'q6_5', '5', 1),
(4852, 31, 'q0_1', '2002', 2),
(4853, 31, 'q0_7', '', 2),
(4854, 31, 'q0_8', 'sasas', 2),
(4855, 31, 'q0_3', '2', 2),
(4856, 31, 'q0_4', '2', 2),
(4857, 31, 'q1_0', '4', 2),
(4858, 31, 'q1_1', '5', 2),
(4859, 31, 'q1_2', '4', 2),
(4860, 31, 'q1_3', '3', 2),
(4861, 31, 'q1_4', '4', 2),
(4862, 31, 'q1_5', '3', 2),
(4863, 31, 'q2_0', '3', 2),
(4864, 31, 'q2_1', '4', 2),
(4865, 31, 'q2_2', '3', 2),
(4866, 31, 'q2_3', '4', 2),
(4867, 31, 'q2_4', '5', 2),
(4868, 31, 'q2_5', '4', 2),
(4869, 31, 'q2_6', '3', 2),
(4870, 31, 'q3_0', '3', 2),
(4871, 31, 'q3_1', '4', 2),
(4872, 31, 'q3_2', '3', 2),
(4873, 31, 'q3_3', '4', 2),
(4874, 31, 'q3_4', '4', 2),
(4875, 31, 'q3_5', '3', 2),
(4876, 31, 'q3_6', '4', 2),
(4877, 31, 'q4_0', '4', 2),
(4878, 31, 'q4_1', '3', 2),
(4879, 31, 'q4_2', '4', 2),
(4880, 31, 'q4_3', '5', 2),
(4881, 31, 'q4_4', '4', 2),
(4882, 31, 'q4_5', '4', 2),
(4883, 31, 'q5_0', '3', 2),
(4884, 31, 'q5_1', '4', 2),
(4885, 31, 'q5_2', '3', 2),
(4886, 31, 'q5_3', '4', 2),
(4887, 31, 'q5_4', '3', 2),
(4888, 31, 'q5_5', '4', 2),
(4889, 31, 'q5_6', '3', 2),
(4890, 31, 'q5_7', '4', 2),
(4891, 31, 'q5_8', '3', 2),
(4892, 31, 'q5_9', '4', 2),
(4893, 31, 'q5_10', '5', 2),
(4894, 31, 'q6_0', '4', 2),
(4895, 31, 'q6_1', '5', 2),
(4896, 31, 'q6_2', '4', 2),
(4897, 31, 'q6_3', '3', 2),
(4898, 31, 'q6_4', '4', 2),
(4899, 31, 'q6_5', '5', 2),
(5001, 31, 'q1_0', '4', 3),
(5002, 31, 'q1_1', '5', 3),
(5003, 31, 'q1_2', '4', 3),
(5004, 31, 'q1_3', '5', 3),
(5005, 31, 'q1_4', '4', 3),
(5006, 31, 'q1_5', '5', 3),
(5054, 31, 'q0_1', '2002', 3),
(5055, 31, 'q0_7', '', 3),
(5056, 31, 'q0_8', 'sasas', 3),
(5057, 31, 'q0_3', '2', 3),
(5058, 31, 'q0_4', '2', 3),
(5059, 31, 'q2_0', '4', 3),
(5060, 31, 'q2_1', '3', 3),
(5061, 31, 'q2_2', '4', 3),
(5062, 31, 'q2_3', '5', 3),
(5063, 31, 'q2_4', '4', 3),
(5064, 31, 'q2_5', '3', 3),
(5065, 31, 'q2_6', '4', 3),
(5066, 31, 'q3_0', '4', 3),
(5067, 31, 'q3_1', '5', 3),
(5068, 31, 'q3_2', '4', 3),
(5069, 31, 'q3_3', '3', 3),
(5070, 31, 'q3_4', '4', 3),
(5071, 31, 'q3_5', '5', 3),
(5072, 31, 'q3_6', '4', 3),
(5073, 31, 'q4_0', '4', 3),
(5074, 31, 'q4_1', '5', 3),
(5075, 31, 'q4_2', '4', 3),
(5076, 31, 'q4_3', '3', 3),
(5077, 31, 'q4_4', '4', 3),
(5078, 31, 'q4_5', '5', 3),
(5079, 31, 'q5_0', '4', 3),
(5080, 31, 'q5_1', '3', 3),
(5081, 31, 'q5_2', '4', 3),
(5082, 31, 'q5_3', '5', 3),
(5083, 31, 'q5_4', '4', 3),
(5084, 31, 'q5_5', '3', 3),
(5085, 31, 'q5_6', '4', 3),
(5086, 31, 'q5_7', '5', 3),
(5087, 31, 'q5_8', '4', 3),
(5088, 31, 'q5_9', '3', 3),
(5089, 31, 'q5_10', '4', 3),
(5090, 31, 'q6_0', '4', 3),
(5091, 31, 'q6_1', '4', 3),
(5092, 31, 'q6_2', '5', 3),
(5093, 31, 'q6_3', '4', 3),
(5094, 31, 'q6_4', '3', 3),
(5095, 31, 'q6_5', '4', 3),
(5096, 32, 'q-2_0', 'Ja', 1),
(5097, 32, 'q-2_1', 'Gruppe D', 1),
(5098, 32, 'q0_0', 'Männlich', 1),
(5099, 32, 'q0_1', '2004', 1),
(5100, 32, 'q0_2', 'Ja', 1),
(5101, 32, 'q0_6', 'Lehramt für Sonderpädagogik', 1),
(5102, 32, 'q0_7', 'fachada', 1),
(5103, 32, 'q0_8', '', 1),
(5104, 32, 'q0_3', '3', 1),
(5105, 32, 'q0_4', '3', 1),
(5106, 32, 'q1_0', '4', 1),
(5107, 32, 'q1_1', '5', 1),
(5108, 32, 'q1_2', '4', 1),
(5109, 32, 'q1_3', '3', 1),
(5110, 32, 'q1_4', '4', 1),
(5111, 32, 'q1_5', '5', 1),
(5112, 32, 'q2_0', '4', 1),
(5113, 32, 'q2_1', '5', 1),
(5114, 32, 'q2_2', '4', 1),
(5115, 32, 'q2_3', '3', 1),
(5116, 32, 'q2_4', '4', 1),
(5117, 32, 'q2_5', '3', 1),
(5118, 32, 'q2_6', '4', 1),
(5119, 32, 'q3_0', '4', 1),
(5120, 32, 'q3_1', '5', 1),
(5121, 32, 'q3_2', '4', 1),
(5122, 32, 'q3_3', '3', 1),
(5123, 32, 'q3_4', '4', 1),
(5124, 32, 'q3_5', '5', 1),
(5125, 32, 'q3_6', '4', 1),
(5126, 32, 'q4_0', '4', 1),
(5127, 32, 'q4_1', '3', 1),
(5128, 32, 'q4_2', '4', 1),
(5129, 32, 'q4_3', '5', 1),
(5130, 32, 'q4_4', '4', 1),
(5131, 32, 'q4_5', '3', 1),
(5132, 32, 'q5_0', '3', 1),
(5133, 32, 'q5_1', '4', 1),
(5134, 32, 'q5_2', '3', 1),
(5135, 32, 'q5_3', '4', 1),
(5136, 32, 'q5_4', '5', 1),
(5137, 32, 'q5_5', '4', 1),
(5138, 32, 'q5_6', '3', 1),
(5139, 32, 'q5_7', '4', 1),
(5140, 32, 'q5_8', '3', 1),
(5141, 32, 'q5_9', '4', 1),
(5142, 32, 'q5_10', '4', 1),
(5143, 32, 'q6_0', '4', 1),
(5144, 32, 'q6_1', '3', 1),
(5145, 32, 'q6_2', '4', 1),
(5146, 32, 'q6_3', '5', 1),
(5147, 32, 'q6_4', '4', 1),
(5148, 32, 'q6_5', '3', 1),
(5149, 32, 'q0_1', '2004', 2),
(5150, 32, 'q0_7', 'fachada', 2),
(5151, 32, 'q0_8', '', 2),
(5152, 32, 'q0_3', '3', 2),
(5153, 32, 'q0_4', '3', 2),
(5154, 32, 'q1_0', '4', 2),
(5155, 32, 'q1_1', '5', 2),
(5156, 32, 'q1_2', '4', 2),
(5157, 32, 'q1_3', '3', 2),
(5158, 32, 'q1_4', '4', 2),
(5159, 32, 'q1_5', '4', 2),
(5160, 32, 'q2_0', '3', 2),
(5161, 32, 'q2_1', '4', 2),
(5162, 32, 'q2_2', '3', 2),
(5163, 32, 'q2_3', '4', 2),
(5164, 32, 'q2_4', '3', 2),
(5165, 32, 'q2_5', '4', 2),
(5166, 32, 'q2_6', '5', 2),
(5167, 32, 'q3_0', '3', 2),
(5168, 32, 'q3_1', '4', 2),
(5169, 32, 'q3_2', '3', 2),
(5170, 32, 'q3_3', '4', 2),
(5171, 32, 'q3_4', '3', 2),
(5172, 32, 'q3_5', '4', 2),
(5173, 32, 'q3_6', '5', 2),
(5174, 32, 'q4_0', '4', 2),
(5175, 32, 'q4_1', '3', 2),
(5176, 32, 'q4_2', '5', 2),
(5177, 32, 'q4_3', '3', 2),
(5178, 32, 'q4_4', '4', 2),
(5179, 32, 'q4_5', '3', 2),
(5180, 32, 'q5_0', '4', 2),
(5181, 32, 'q5_1', '3', 2),
(5182, 32, 'q5_2', '4', 2),
(5183, 32, 'q5_3', '5', 2),
(5184, 32, 'q5_4', '4', 2),
(5185, 32, 'q5_5', '3', 2),
(5186, 32, 'q5_6', '4', 2),
(5187, 32, 'q5_7', '3', 2),
(5188, 32, 'q5_8', '4', 2),
(5189, 32, 'q5_9', '4', 2),
(5190, 32, 'q5_10', '5', 2),
(5191, 32, 'q6_0', '4', 2),
(5192, 32, 'q6_1', '3', 2),
(5193, 32, 'q6_2', '4', 2),
(5194, 32, 'q6_3', '3', 2),
(5195, 32, 'q6_4', '4', 2),
(5196, 32, 'q6_5', '3', 2),
(5197, 32, 'q0_1', '2004', 3),
(5198, 32, 'q0_7', 'fachada', 3),
(5199, 32, 'q0_8', '', 3),
(5200, 32, 'q0_3', '3', 3),
(5201, 32, 'q0_4', '3', 3),
(5202, 32, 'q1_0', '4', 3),
(5203, 32, 'q1_1', '5', 3),
(5204, 32, 'q1_2', '4', 3),
(5205, 32, 'q1_3', '3', 3),
(5206, 32, 'q1_4', '4', 3),
(5207, 32, 'q1_5', '5', 3),
(5208, 32, 'q2_0', '4', 3),
(5209, 32, 'q2_1', '3', 3),
(5210, 32, 'q2_2', '4', 3),
(5211, 32, 'q2_3', '5', 3),
(5212, 32, 'q2_4', '4', 3),
(5213, 32, 'q2_5', '3', 3),
(5214, 32, 'q2_6', '4', 3),
(5215, 32, 'q3_0', '4', 3),
(5216, 32, 'q3_1', '5', 3),
(5217, 32, 'q3_2', '4', 3),
(5218, 32, 'q3_3', '3', 3),
(5219, 32, 'q3_4', '4', 3),
(5220, 32, 'q3_5', '3', 3),
(5221, 32, 'q3_6', '4', 3),
(5222, 32, 'q4_0', '4', 3),
(5223, 32, 'q4_1', '3', 3),
(5224, 32, 'q4_2', '4', 3),
(5225, 32, 'q4_3', '3', 3),
(5226, 32, 'q4_4', '4', 3),
(5227, 32, 'q4_5', '4', 3),
(5228, 32, 'q5_0', '4', 3),
(5229, 32, 'q5_1', '4', 3),
(5230, 32, 'q5_2', '5', 3),
(5231, 32, 'q5_3', '4', 3),
(5232, 32, 'q5_4', '3', 3),
(5233, 32, 'q5_5', '4', 3),
(5234, 32, 'q5_6', '3', 3),
(5235, 32, 'q5_7', '4', 3),
(5236, 32, 'q5_8', '3', 3),
(5237, 32, 'q5_9', '4', 3),
(5238, 32, 'q5_10', '5', 3),
(5239, 32, 'q6_0', '4', 3),
(5240, 32, 'q6_1', '5', 3),
(5241, 32, 'q6_2', '4', 3),
(5242, 32, 'q6_3', '3', 3),
(5243, 32, 'q6_4', '4', 3),
(5244, 32, 'q6_5', '5', 3),
(5245, 33, 'q-2_0', 'Nein', 1),
(5246, 33, 'q-2_1', 'Gruppe A', 1);

-- --------------------------------------------------------

--
-- Table structure for table `scores`
--

CREATE TABLE `scores` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `attempt_number` int(11) NOT NULL,
  `category` varchar(100) NOT NULL,
  `score` double NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `scores`
--

INSERT INTO `scores` (`id`, `user_id`, `attempt_number`, `category`, `score`) VALUES
(36, 4, 1, 'Suchen, Verarbeiten und Aufbewahren', 75),
(37, 4, 1, 'Kommunikation und Kollaborieren', 57),
(38, 4, 1, 'Produzieren und Präsentieren', 74),
(39, 4, 1, 'Schützen und sicher Agieren', 75),
(40, 4, 1, 'Problemlösen und Handeln', 74),
(41, 4, 1, 'Analysieren und Reflektieren', 75),
(42, 4, 1, 'overall', 72),
(85, 5, 1, 'Suchen, Verarbeiten und Aufbewahren', 69),
(86, 5, 1, 'Kommunikation und Kollaborieren', 71),
(87, 5, 1, 'Produzieren und Präsentieren', 69),
(88, 5, 1, 'Schützen und sicher Agieren', 69),
(89, 5, 1, 'Problemlösen und Handeln', 68),
(90, 5, 1, 'Analysieren und Reflektieren', 67),
(91, 5, 1, 'overall', 69),
(92, 5, 2, 'Suchen, Verarbeiten und Aufbewahren', 75),
(93, 5, 2, 'Kommunikation und Kollaborieren', 74),
(94, 5, 2, 'Produzieren und Präsentieren', 74),
(95, 5, 2, 'Schützen und sicher Agieren', 75),
(96, 5, 2, 'Problemlösen und Handeln', 71),
(97, 5, 2, 'Analysieren und Reflektieren', 75),
(98, 5, 2, 'overall', 74),
(120, 6, 1, 'Suchen, Verarbeiten und Aufbewahren', 67),
(121, 6, 1, 'Kommunikation und Kollaborieren', 67),
(122, 6, 1, 'Produzieren und Präsentieren', 67),
(123, 6, 1, 'Schützen und sicher Agieren', 67),
(124, 6, 1, 'Problemlösen und Handeln', 67),
(125, 6, 1, 'Analysieren und Reflektieren', 67),
(126, 6, 1, 'overall', 67),
(127, 6, 2, 'Suchen, Verarbeiten und Aufbewahren', 83),
(128, 6, 2, 'Kommunikation und Kollaborieren', 83),
(129, 6, 2, 'Produzieren und Präsentieren', 83),
(130, 6, 2, 'Schützen und sicher Agieren', 83),
(131, 6, 2, 'Problemlösen und Handeln', 83),
(132, 6, 2, 'Analysieren und Reflektieren', 83),
(133, 6, 2, 'overall', 83),
(148, 8, 1, 'Suchen, Verarbeiten und Aufbewahren', 31),
(149, 8, 1, 'Kommunikation und Kollaborieren', 50),
(150, 8, 1, 'Produzieren und Präsentieren', 50),
(151, 8, 1, 'Schützen und sicher Agieren', 42),
(152, 8, 1, 'Problemlösen und Handeln', 47),
(153, 8, 1, 'Analysieren und Reflektieren', 58),
(154, 8, 1, 'overall', 47),
(162, 8, 2, 'Suchen, Verarbeiten und Aufbewahren', 83),
(163, 8, 2, 'Kommunikation und Kollaborieren', 83),
(164, 8, 2, 'Produzieren und Präsentieren', 83),
(165, 8, 2, 'Schützen und sicher Agieren', 83),
(166, 8, 2, 'Problemlösen und Handeln', 83),
(167, 8, 2, 'Analysieren und Reflektieren', 83),
(168, 8, 2, 'overall', 83),
(176, 8, 3, 'Suchen, Verarbeiten und Aufbewahren', 100),
(177, 8, 3, 'Kommunikation und Kollaborieren', 100),
(178, 8, 3, 'Produzieren und Präsentieren', 100),
(179, 8, 3, 'Schützen und sicher Agieren', 100),
(180, 8, 3, 'Problemlösen und Handeln', 100),
(181, 8, 3, 'Analysieren und Reflektieren', 100),
(182, 8, 3, 'overall', 100),
(183, 4, 2, 'Suchen, Verarbeiten und Aufbewahren', 75),
(184, 4, 2, 'Kommunikation und Kollaborieren', 74),
(185, 4, 2, 'Produzieren und Präsentieren', 74),
(186, 4, 2, 'Schützen und sicher Agieren', 75),
(187, 4, 2, 'Problemlösen und Handeln', 41),
(188, 4, 2, 'Analysieren und Reflektieren', 64),
(189, 4, 2, 'overall', 64),
(281, 5, 3, 'Suchen, Verarbeiten und Aufbewahren', 75),
(282, 5, 3, 'Kommunikation und Kollaborieren', 74),
(283, 5, 3, 'Produzieren und Präsentieren', 74),
(284, 5, 3, 'Schützen und sicher Agieren', 75),
(285, 5, 3, 'Problemlösen und Handeln', 74),
(286, 5, 3, 'Analysieren und Reflektieren', 75),
(287, 5, 3, 'overall', 74),
(288, 6, 3, 'Suchen, Verarbeiten und Aufbewahren', 100),
(289, 6, 3, 'Kommunikation und Kollaborieren', 100),
(290, 6, 3, 'Produzieren und Präsentieren', 100),
(291, 6, 3, 'Schützen und sicher Agieren', 100),
(292, 6, 3, 'Problemlösen und Handeln', 100),
(293, 6, 3, 'Analysieren und Reflektieren', 100),
(294, 6, 3, 'overall', 100),
(309, 4, 3, 'Suchen, Verarbeiten und Aufbewahren', 100),
(310, 4, 3, 'Kommunikation und Kollaborieren', 100),
(311, 4, 3, 'Produzieren und Präsentieren', 100),
(312, 4, 3, 'Schützen und sicher Agieren', 100),
(313, 4, 3, 'Problemlösen und Handeln', 100),
(314, 4, 3, 'Analysieren und Reflektieren', 100),
(315, 4, 3, 'overall', 100),
(533, 24, 1, 'Suchen, Verarbeiten und Aufbewahren', 33),
(534, 24, 1, 'Kommunikation und Kollaborieren', 33),
(535, 24, 1, 'Produzieren und Präsentieren', 33),
(536, 24, 1, 'Schützen und sicher Agieren', 33),
(537, 24, 1, 'Problemlösen und Handeln', 33),
(538, 24, 1, 'Analysieren und Reflektieren', 33),
(539, 24, 1, 'overall', 33),
(540, 24, 2, 'Suchen, Verarbeiten und Aufbewahren', 50),
(541, 24, 2, 'Kommunikation und Kollaborieren', 33),
(542, 24, 2, 'Produzieren und Präsentieren', 43),
(543, 24, 2, 'Schützen und sicher Agieren', 50),
(544, 24, 2, 'Problemlösen und Handeln', 42),
(545, 24, 2, 'Analysieren und Reflektieren', 47),
(546, 24, 2, 'overall', 44),
(547, 24, 3, 'Suchen, Verarbeiten und Aufbewahren', 42),
(548, 24, 3, 'Kommunikation und Kollaborieren', 33),
(549, 24, 3, 'Produzieren und Präsentieren', 40),
(550, 24, 3, 'Schützen und sicher Agieren', 33),
(551, 24, 3, 'Problemlösen und Handeln', 29),
(552, 24, 3, 'Analysieren und Reflektieren', 22),
(553, 24, 3, 'overall', 33),
(554, 25, 1, 'Suchen, Verarbeiten und Aufbewahren', 0),
(555, 25, 1, 'Kommunikation und Kollaborieren', 5),
(556, 25, 1, 'Produzieren und Präsentieren', 0),
(557, 25, 1, 'Schützen und sicher Agieren', 0),
(558, 25, 1, 'Problemlösen und Handeln', 0),
(559, 25, 1, 'Analysieren und Reflektieren', 11),
(560, 25, 1, 'overall', 2),
(561, 25, 2, 'Suchen, Verarbeiten und Aufbewahren', 14),
(562, 25, 2, 'Kommunikation und Kollaborieren', 12),
(563, 25, 2, 'Produzieren und Präsentieren', 12),
(564, 25, 2, 'Schützen und sicher Agieren', 17),
(565, 25, 2, 'Problemlösen und Handeln', 39),
(566, 25, 2, 'Analysieren und Reflektieren', 36),
(567, 25, 2, 'overall', 23),
(575, 26, 1, 'Suchen, Verarbeiten und Aufbewahren', 31),
(576, 26, 1, 'Kommunikation und Kollaborieren', 7),
(577, 26, 1, 'Produzieren und Präsentieren', 24),
(578, 26, 1, 'Schützen und sicher Agieren', 33),
(579, 26, 1, 'Problemlösen und Handeln', 29),
(580, 26, 1, 'Analysieren und Reflektieren', 39),
(581, 26, 1, 'overall', 27),
(582, 26, 2, 'Suchen, Verarbeiten und Aufbewahren', 39),
(583, 26, 2, 'Kommunikation und Kollaborieren', 29),
(584, 26, 2, 'Produzieren und Präsentieren', 36),
(585, 26, 2, 'Schützen und sicher Agieren', 31),
(586, 26, 2, 'Problemlösen und Handeln', 35),
(587, 26, 2, 'Analysieren und Reflektieren', 28),
(588, 26, 2, 'overall', 33),
(596, 27, 1, 'Suchen, Verarbeiten und Aufbewahren', 0),
(597, 27, 1, 'Kommunikation und Kollaborieren', 10),
(598, 27, 1, 'Produzieren und Präsentieren', 19),
(599, 27, 1, 'Schützen und sicher Agieren', 28),
(600, 27, 1, 'Problemlösen und Handeln', 33),
(601, 27, 1, 'Analysieren und Reflektieren', 50),
(602, 27, 1, 'overall', 24),
(603, 27, 2, 'Suchen, Verarbeiten und Aufbewahren', 61),
(604, 27, 2, 'Kommunikation und Kollaborieren', 76),
(605, 27, 2, 'Produzieren und Präsentieren', 79),
(606, 27, 2, 'Schützen und sicher Agieren', 53),
(607, 27, 2, 'Problemlösen und Handeln', 23),
(608, 27, 2, 'Analysieren und Reflektieren', 39),
(609, 27, 2, 'overall', 52),
(610, 27, 3, 'Suchen, Verarbeiten und Aufbewahren', 53),
(611, 27, 3, 'Kommunikation und Kollaborieren', 50),
(612, 27, 3, 'Produzieren und Präsentieren', 33),
(613, 27, 3, 'Schützen und sicher Agieren', 42),
(614, 27, 3, 'Problemlösen und Handeln', 44),
(615, 27, 3, 'Analysieren und Reflektieren', 47),
(616, 27, 3, 'overall', 45),
(617, 26, 3, 'Suchen, Verarbeiten und Aufbewahren', 47),
(618, 26, 3, 'Kommunikation und Kollaborieren', 33),
(619, 26, 3, 'Produzieren und Präsentieren', 60),
(620, 26, 3, 'Schützen und sicher Agieren', 42),
(621, 26, 3, 'Problemlösen und Handeln', 50),
(622, 26, 3, 'Analysieren und Reflektieren', 42),
(623, 26, 3, 'overall', 46),
(624, 25, 3, 'Suchen, Verarbeiten und Aufbewahren', 33),
(625, 25, 3, 'Kommunikation und Kollaborieren', 48),
(626, 25, 3, 'Produzieren und Präsentieren', 43),
(627, 25, 3, 'Schützen und sicher Agieren', 22),
(628, 25, 3, 'Problemlösen und Handeln', 55),
(629, 25, 3, 'Analysieren und Reflektieren', 100),
(630, 25, 3, 'overall', 50),
(715, 29, 1, 'Suchen, Verarbeiten und Aufbewahren', 64),
(716, 29, 1, 'Kommunikation und Kollaborieren', 57),
(717, 29, 1, 'Produzieren und Präsentieren', 57),
(718, 29, 1, 'Schützen und sicher Agieren', 58),
(719, 29, 1, 'Problemlösen und Handeln', 58),
(720, 29, 1, 'Analysieren und Reflektieren', 58),
(721, 29, 1, 'overall', 59),
(722, 29, 2, 'Suchen, Verarbeiten und Aufbewahren', 58),
(723, 29, 2, 'Kommunikation und Kollaborieren', 57),
(724, 29, 2, 'Produzieren und Präsentieren', 57),
(725, 29, 2, 'Schützen und sicher Agieren', 58),
(726, 29, 2, 'Problemlösen und Handeln', 59),
(727, 29, 2, 'Analysieren und Reflektieren', 58),
(728, 29, 2, 'overall', 58),
(729, 29, 3, 'Suchen, Verarbeiten und Aufbewahren', 69),
(730, 29, 3, 'Kommunikation und Kollaborieren', 69),
(731, 29, 3, 'Produzieren und Präsentieren', 74),
(732, 29, 3, 'Schützen und sicher Agieren', 72),
(733, 29, 3, 'Problemlösen und Handeln', 70),
(734, 29, 3, 'Analysieren und Reflektieren', 69),
(735, 29, 3, 'overall', 71),
(736, 30, 1, 'Suchen, Verarbeiten und Aufbewahren', 58),
(737, 30, 1, 'Kommunikation und Kollaborieren', 55),
(738, 30, 1, 'Produzieren und Präsentieren', 57),
(739, 30, 1, 'Schützen und sicher Agieren', 64),
(740, 30, 1, 'Problemlösen und Handeln', 61),
(741, 30, 1, 'Analysieren und Reflektieren', 58),
(742, 30, 1, 'overall', 59),
(750, 30, 2, 'Suchen, Verarbeiten und Aufbewahren', 61),
(751, 30, 2, 'Kommunikation und Kollaborieren', 64),
(752, 30, 2, 'Produzieren und Präsentieren', 57),
(753, 30, 2, 'Schützen und sicher Agieren', 64),
(754, 30, 2, 'Problemlösen und Handeln', 61),
(755, 30, 2, 'Analysieren und Reflektieren', 64),
(756, 30, 2, 'overall', 62),
(771, 30, 3, 'Suchen, Verarbeiten und Aufbewahren', 75),
(772, 30, 3, 'Kommunikation und Kollaborieren', 74),
(773, 30, 3, 'Produzieren und Präsentieren', 69),
(774, 30, 3, 'Schützen und sicher Agieren', 61),
(775, 30, 3, 'Problemlösen und Handeln', 64),
(776, 30, 3, 'Analysieren und Reflektieren', 67),
(777, 30, 3, 'overall', 68),
(778, 31, 1, 'Suchen, Verarbeiten und Aufbewahren', 58),
(779, 31, 1, 'Kommunikation und Kollaborieren', 60),
(780, 31, 1, 'Produzieren und Präsentieren', 69),
(781, 31, 1, 'Schützen und sicher Agieren', 69),
(782, 31, 1, 'Problemlösen und Handeln', 68),
(783, 31, 1, 'Analysieren und Reflektieren', 69),
(784, 31, 1, 'overall', 66),
(785, 31, 2, 'Suchen, Verarbeiten und Aufbewahren', 64),
(786, 31, 2, 'Kommunikation und Kollaborieren', 62),
(787, 31, 2, 'Produzieren und Präsentieren', 60),
(788, 31, 2, 'Schützen und sicher Agieren', 67),
(789, 31, 2, 'Problemlösen und Handeln', 61),
(790, 31, 2, 'Analysieren und Reflektieren', 69),
(791, 31, 2, 'overall', 63),
(806, 31, 3, 'Suchen, Verarbeiten und Aufbewahren', 75),
(807, 31, 3, 'Kommunikation und Kollaborieren', 64),
(808, 31, 3, 'Produzieren und Präsentieren', 69),
(809, 31, 3, 'Schützen und sicher Agieren', 69),
(810, 31, 3, 'Problemlösen und Handeln', 65),
(811, 31, 3, 'Analysieren und Reflektieren', 67),
(812, 31, 3, 'overall', 68),
(813, 32, 1, 'Suchen, Verarbeiten und Aufbewahren', 69),
(814, 32, 1, 'Kommunikation und Kollaborieren', 64),
(815, 32, 1, 'Produzieren und Präsentieren', 69),
(816, 32, 1, 'Schützen und sicher Agieren', 64),
(817, 32, 1, 'Problemlösen und Handeln', 62),
(818, 32, 1, 'Analysieren und Reflektieren', 64),
(819, 32, 1, 'overall', 65),
(820, 32, 2, 'Suchen, Verarbeiten und Aufbewahren', 67),
(821, 32, 2, 'Kommunikation und Kollaborieren', 62),
(822, 32, 2, 'Produzieren und Präsentieren', 62),
(823, 32, 2, 'Schützen und sicher Agieren', 61),
(824, 32, 2, 'Problemlösen und Handeln', 65),
(825, 32, 2, 'Analysieren und Reflektieren', 58),
(826, 32, 2, 'overall', 63),
(827, 32, 3, 'Suchen, Verarbeiten und Aufbewahren', 69),
(828, 32, 3, 'Kommunikation und Kollaborieren', 64),
(829, 32, 3, 'Produzieren und Präsentieren', 64),
(830, 32, 3, 'Schützen und sicher Agieren', 61),
(831, 32, 3, 'Problemlösen und Handeln', 65),
(832, 32, 3, 'Analysieren und Reflektieren', 69),
(833, 32, 3, 'overall', 66);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `user_code` varchar(15) NOT NULL,
  `attempt_number` int(11) DEFAULT 1,
  `is_complete` tinyint(1) DEFAULT 0,
  `current_section` int(11) DEFAULT -2,
  `datenschutz_consent` tinyint(1) DEFAULT 0,
  `unterschrift` varchar(255) DEFAULT NULL,
  `t1_timestamp` datetime DEFAULT NULL,
  `t2_timestamp` datetime DEFAULT NULL,
  `t3_timestamp` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `user_code`, `attempt_number`, `is_complete`, `current_section`, `datenschutz_consent`, `unterschrift`, `t1_timestamp`, `t2_timestamp`, `t3_timestamp`) VALUES
(4, 'AA-AA-11-AA', 3, 1, 6, 1, 'AA-AA-11-AA', '2025-03-30 13:30:52', '2025-03-30 20:31:53', '2025-04-03 14:52:17'),
(5, 'BB-BB-01-BB', 3, 1, 6, 1, 'BB-BB-01-BB', '2025-03-30 14:46:40', '2025-03-30 14:48:36', '2025-04-03 13:36:11'),
(6, 'CC-CC-11-CC', 3, 1, 6, 1, 'CC-CC-11-CC', '2025-03-30 18:41:26', '2025-03-30 18:43:08', '2025-04-03 13:40:09'),
(8, 'DD-DD-11-DD', 3, 1, 6, 1, 'DD-DD-11-DD', '2025-03-30 19:12:21', '2025-03-30 19:16:24', '2025-03-30 19:23:08'),
(24, 'BB-AA-11-AA', 3, 1, 6, 1, 'BB-AA-11-AA', '2025-04-07 10:44:59', '2025-04-07 10:49:06', '2025-04-07 10:51:54'),
(25, 'BB-CC-30-HH', 3, 1, 6, 1, 'BB-CC-30-HH', '2025-04-07 11:03:12', '2025-04-07 11:07:06', '2025-04-07 15:10:57'),
(26, 'KK-JJ-05-HG', 3, 1, 6, 1, 'KK-JJ-05-HG', '2025-04-07 11:14:31', '2025-04-07 11:15:56', '2025-04-07 15:08:04'),
(27, 'FF-FF-13-GG', 3, 1, 6, 1, 'FF-FF-13-GG', '2025-04-07 11:22:15', '2025-04-07 11:23:26', '2025-04-07 11:25:34'),
(29, 'KK-KK-25-KK', 3, 1, 6, 1, 'KK-KK-25-KK', '2025-04-07 23:49:14', '2025-04-07 23:51:14', '2025-04-07 23:52:51'),
(30, 'BB-BB-25-BB', 3, 1, 6, 1, 'BB-BB-25-BB', '2025-04-08 18:38:30', '2025-04-08 18:58:20', '2025-04-08 19:09:42'),
(31, 'CC-CC-25-CC', 3, 1, 6, 1, 'CC-CC-25-CC', '2025-04-08 19:12:46', '2025-04-08 19:15:09', '2025-04-08 19:55:11'),
(32, 'DD-DD-25-DD', 3, 1, 6, 1, 'DD-DD-25-DD', '2025-04-08 19:58:53', '2025-04-08 20:00:04', '2025-04-08 20:01:22'),
(33, 'AA-AA-15-AA', 1, 0, -2, 0, NULL, NULL, NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `dashboard_users`
--
ALTER TABLE `dashboard_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `open_ended_responses`
--
ALTER TABLE `open_ended_responses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `pre_survey_responses`
--
ALTER TABLE `pre_survey_responses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `responses`
--
ALTER TABLE `responses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `scores`
--
ALTER TABLE `scores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_category_attempt` (`user_id`,`category`,`attempt_number`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_code` (`user_code`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `dashboard_users`
--
ALTER TABLE `dashboard_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `open_ended_responses`
--
ALTER TABLE `open_ended_responses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=144;

--
-- AUTO_INCREMENT for table `pre_survey_responses`
--
ALTER TABLE `pre_survey_responses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=69;

--
-- AUTO_INCREMENT for table `responses`
--
ALTER TABLE `responses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5247;

--
-- AUTO_INCREMENT for table `scores`
--
ALTER TABLE `scores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=834;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `open_ended_responses`
--
ALTER TABLE `open_ended_responses`
  ADD CONSTRAINT `open_ended_responses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pre_survey_responses`
--
ALTER TABLE `pre_survey_responses`
  ADD CONSTRAINT `pre_survey_responses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `responses`
--
ALTER TABLE `responses`
  ADD CONSTRAINT `responses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `scores`
--
ALTER TABLE `scores`
  ADD CONSTRAINT `scores_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
--
-- Database: `phpmyadmin`
--
CREATE DATABASE IF NOT EXISTS `phpmyadmin` DEFAULT CHARACTER SET utf8 COLLATE utf8_bin;
USE `phpmyadmin`;

-- --------------------------------------------------------

--
-- Table structure for table `pma__bookmark`
--

CREATE TABLE `pma__bookmark` (
  `id` int(10) UNSIGNED NOT NULL,
  `dbase` varchar(255) NOT NULL DEFAULT '',
  `user` varchar(255) NOT NULL DEFAULT '',
  `label` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `query` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Bookmarks';

-- --------------------------------------------------------

--
-- Table structure for table `pma__central_columns`
--

CREATE TABLE `pma__central_columns` (
  `db_name` varchar(64) NOT NULL,
  `col_name` varchar(64) NOT NULL,
  `col_type` varchar(64) NOT NULL,
  `col_length` text DEFAULT NULL,
  `col_collation` varchar(64) NOT NULL,
  `col_isNull` tinyint(1) NOT NULL,
  `col_extra` varchar(255) DEFAULT '',
  `col_default` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Central list of columns';

-- --------------------------------------------------------

--
-- Table structure for table `pma__column_info`
--

CREATE TABLE `pma__column_info` (
  `id` int(5) UNSIGNED NOT NULL,
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `table_name` varchar(64) NOT NULL DEFAULT '',
  `column_name` varchar(64) NOT NULL DEFAULT '',
  `comment` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `mimetype` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `transformation` varchar(255) NOT NULL DEFAULT '',
  `transformation_options` varchar(255) NOT NULL DEFAULT '',
  `input_transformation` varchar(255) NOT NULL DEFAULT '',
  `input_transformation_options` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Column information for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__designer_settings`
--

CREATE TABLE `pma__designer_settings` (
  `username` varchar(64) NOT NULL,
  `settings_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Settings related to Designer';

-- --------------------------------------------------------

--
-- Table structure for table `pma__export_templates`
--

CREATE TABLE `pma__export_templates` (
  `id` int(5) UNSIGNED NOT NULL,
  `username` varchar(64) NOT NULL,
  `export_type` varchar(10) NOT NULL,
  `template_name` varchar(64) NOT NULL,
  `template_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Saved export templates';

-- --------------------------------------------------------

--
-- Table structure for table `pma__favorite`
--

CREATE TABLE `pma__favorite` (
  `username` varchar(64) NOT NULL,
  `tables` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Favorite tables';

-- --------------------------------------------------------

--
-- Table structure for table `pma__history`
--

CREATE TABLE `pma__history` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(64) NOT NULL DEFAULT '',
  `db` varchar(64) NOT NULL DEFAULT '',
  `table` varchar(64) NOT NULL DEFAULT '',
  `timevalue` timestamp NOT NULL DEFAULT current_timestamp(),
  `sqlquery` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='SQL history for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__navigationhiding`
--

CREATE TABLE `pma__navigationhiding` (
  `username` varchar(64) NOT NULL,
  `item_name` varchar(64) NOT NULL,
  `item_type` varchar(64) NOT NULL,
  `db_name` varchar(64) NOT NULL,
  `table_name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Hidden items of navigation tree';

-- --------------------------------------------------------

--
-- Table structure for table `pma__pdf_pages`
--

CREATE TABLE `pma__pdf_pages` (
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `page_nr` int(10) UNSIGNED NOT NULL,
  `page_descr` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='PDF relation pages for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__recent`
--

CREATE TABLE `pma__recent` (
  `username` varchar(64) NOT NULL,
  `tables` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Recently accessed tables';

--
-- Dumping data for table `pma__recent`
--

INSERT INTO `pma__recent` (`username`, `tables`) VALUES
('root', '[{\"db\":\"open_digi_db\",\"table\":\"users\"},{\"db\":\"open_digi_db\",\"table\":\"responses\"},{\"db\":\"open_digi_db\",\"table\":\"open_ended_responses\"},{\"db\":\"open_digi_db\",\"table\":\"dashboard_users\"},{\"db\":\"open_digi_db\",\"table\":\"pre_survey_responses\"},{\"db\":\"open_digi_db\",\"table\":\"scores\"},{\"db\":\"open_digi_db\",\"table\":\"code\"},{\"db\":\"open_digi_db\",\"table\":\"userdata\"}]');

-- --------------------------------------------------------

--
-- Table structure for table `pma__relation`
--

CREATE TABLE `pma__relation` (
  `master_db` varchar(64) NOT NULL DEFAULT '',
  `master_table` varchar(64) NOT NULL DEFAULT '',
  `master_field` varchar(64) NOT NULL DEFAULT '',
  `foreign_db` varchar(64) NOT NULL DEFAULT '',
  `foreign_table` varchar(64) NOT NULL DEFAULT '',
  `foreign_field` varchar(64) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Relation table';

-- --------------------------------------------------------

--
-- Table structure for table `pma__savedsearches`
--

CREATE TABLE `pma__savedsearches` (
  `id` int(5) UNSIGNED NOT NULL,
  `username` varchar(64) NOT NULL DEFAULT '',
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `search_name` varchar(64) NOT NULL DEFAULT '',
  `search_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Saved searches';

-- --------------------------------------------------------

--
-- Table structure for table `pma__table_coords`
--

CREATE TABLE `pma__table_coords` (
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `table_name` varchar(64) NOT NULL DEFAULT '',
  `pdf_page_number` int(11) NOT NULL DEFAULT 0,
  `x` float UNSIGNED NOT NULL DEFAULT 0,
  `y` float UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Table coordinates for phpMyAdmin PDF output';

-- --------------------------------------------------------

--
-- Table structure for table `pma__table_info`
--

CREATE TABLE `pma__table_info` (
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `table_name` varchar(64) NOT NULL DEFAULT '',
  `display_field` varchar(64) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Table information for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__table_uiprefs`
--

CREATE TABLE `pma__table_uiprefs` (
  `username` varchar(64) NOT NULL,
  `db_name` varchar(64) NOT NULL,
  `table_name` varchar(64) NOT NULL,
  `prefs` text NOT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Tables'' UI preferences';

-- --------------------------------------------------------

--
-- Table structure for table `pma__tracking`
--

CREATE TABLE `pma__tracking` (
  `db_name` varchar(64) NOT NULL,
  `table_name` varchar(64) NOT NULL,
  `version` int(10) UNSIGNED NOT NULL,
  `date_created` datetime NOT NULL,
  `date_updated` datetime NOT NULL,
  `schema_snapshot` text NOT NULL,
  `schema_sql` text DEFAULT NULL,
  `data_sql` longtext DEFAULT NULL,
  `tracking` set('UPDATE','REPLACE','INSERT','DELETE','TRUNCATE','CREATE DATABASE','ALTER DATABASE','DROP DATABASE','CREATE TABLE','ALTER TABLE','RENAME TABLE','DROP TABLE','CREATE INDEX','DROP INDEX','CREATE VIEW','ALTER VIEW','DROP VIEW') DEFAULT NULL,
  `tracking_active` int(1) UNSIGNED NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Database changes tracking for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__userconfig`
--

CREATE TABLE `pma__userconfig` (
  `username` varchar(64) NOT NULL,
  `timevalue` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `config_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='User preferences storage for phpMyAdmin';

--
-- Dumping data for table `pma__userconfig`
--

INSERT INTO `pma__userconfig` (`username`, `timevalue`, `config_data`) VALUES
('root', '2025-04-20 18:32:29', '{\"Console\\/Mode\":\"collapse\",\"lang\":\"en_GB\"}');

-- --------------------------------------------------------

--
-- Table structure for table `pma__usergroups`
--

CREATE TABLE `pma__usergroups` (
  `usergroup` varchar(64) NOT NULL,
  `tab` varchar(64) NOT NULL,
  `allowed` enum('Y','N') NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='User groups with configured menu items';

-- --------------------------------------------------------

--
-- Table structure for table `pma__users`
--

CREATE TABLE `pma__users` (
  `username` varchar(64) NOT NULL,
  `usergroup` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Users and their assignments to user groups';

--
-- Indexes for dumped tables
--

--
-- Indexes for table `pma__bookmark`
--
ALTER TABLE `pma__bookmark`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `pma__central_columns`
--
ALTER TABLE `pma__central_columns`
  ADD PRIMARY KEY (`db_name`,`col_name`);

--
-- Indexes for table `pma__column_info`
--
ALTER TABLE `pma__column_info`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `db_name` (`db_name`,`table_name`,`column_name`);

--
-- Indexes for table `pma__designer_settings`
--
ALTER TABLE `pma__designer_settings`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__export_templates`
--
ALTER TABLE `pma__export_templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `u_user_type_template` (`username`,`export_type`,`template_name`);

--
-- Indexes for table `pma__favorite`
--
ALTER TABLE `pma__favorite`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__history`
--
ALTER TABLE `pma__history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `username` (`username`,`db`,`table`,`timevalue`);

--
-- Indexes for table `pma__navigationhiding`
--
ALTER TABLE `pma__navigationhiding`
  ADD PRIMARY KEY (`username`,`item_name`,`item_type`,`db_name`,`table_name`);

--
-- Indexes for table `pma__pdf_pages`
--
ALTER TABLE `pma__pdf_pages`
  ADD PRIMARY KEY (`page_nr`),
  ADD KEY `db_name` (`db_name`);

--
-- Indexes for table `pma__recent`
--
ALTER TABLE `pma__recent`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__relation`
--
ALTER TABLE `pma__relation`
  ADD PRIMARY KEY (`master_db`,`master_table`,`master_field`),
  ADD KEY `foreign_field` (`foreign_db`,`foreign_table`);

--
-- Indexes for table `pma__savedsearches`
--
ALTER TABLE `pma__savedsearches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `u_savedsearches_username_dbname` (`username`,`db_name`,`search_name`);

--
-- Indexes for table `pma__table_coords`
--
ALTER TABLE `pma__table_coords`
  ADD PRIMARY KEY (`db_name`,`table_name`,`pdf_page_number`);

--
-- Indexes for table `pma__table_info`
--
ALTER TABLE `pma__table_info`
  ADD PRIMARY KEY (`db_name`,`table_name`);

--
-- Indexes for table `pma__table_uiprefs`
--
ALTER TABLE `pma__table_uiprefs`
  ADD PRIMARY KEY (`username`,`db_name`,`table_name`);

--
-- Indexes for table `pma__tracking`
--
ALTER TABLE `pma__tracking`
  ADD PRIMARY KEY (`db_name`,`table_name`,`version`);

--
-- Indexes for table `pma__userconfig`
--
ALTER TABLE `pma__userconfig`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__usergroups`
--
ALTER TABLE `pma__usergroups`
  ADD PRIMARY KEY (`usergroup`,`tab`,`allowed`);

--
-- Indexes for table `pma__users`
--
ALTER TABLE `pma__users`
  ADD PRIMARY KEY (`username`,`usergroup`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `pma__bookmark`
--
ALTER TABLE `pma__bookmark`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__column_info`
--
ALTER TABLE `pma__column_info`
  MODIFY `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__export_templates`
--
ALTER TABLE `pma__export_templates`
  MODIFY `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__history`
--
ALTER TABLE `pma__history`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__pdf_pages`
--
ALTER TABLE `pma__pdf_pages`
  MODIFY `page_nr` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__savedsearches`
--
ALTER TABLE `pma__savedsearches`
  MODIFY `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT;
--
-- Database: `test`
--
CREATE DATABASE IF NOT EXISTS `test` DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci;
USE `test`;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
