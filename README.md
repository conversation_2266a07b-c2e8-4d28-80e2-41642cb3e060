# Open-Digi Survey Website

## Description

This project is the frontend and backend for the Open-Digi research project's survey website at the University of Rostock. It allows users (students) to generate a unique code, participate in a multi-stage survey (T1, T2, T3) about digital competencies, receive feedback based on their answers (including competency charts), and view their results. An administrative dashboard is included for viewing aggregated user data.

The website is built using vanilla HTML, CSS, and JavaScript for the frontend, and PHP with a MariaDB/MySQL database for the backend.

## Features

- Personalized code generation based on user input.
- User login using the generated code.
- Multi-stage survey administration (T1, T2, T3) with progress saving.
- Dynamic display of survey questions based on user group (A, B, C, D) and attempt number.
- Calculation and display of competency scores based on survey responses.
- Visual results presentation using Chart.js.
- Collection of open-ended reflection responses at different stages.
- PDF export of user results.
- Secure administrative dashboard for viewing and exporting user data.
- Static pages for Project Information, Team, Data Protection, and Courses.

## Technology Stack

- **Frontend:** HTML5, CSS3, Vanilla JavaScript (ES6+)
- **Backend:** PHP (Version [e.g., 8.0 or higher recommended])
- **Database:** MariaDB / MySQL
- **JavaScript Libraries (via CDN):**
  - Chart.js (for results visualization)
  - SweetAlert2 (for user notifications)
  - html2canvas & jsPDF (for PDF export)
  - canvas-confetti (for visual feedback)
- **Server Environment (Recommended):** LAMP (Linux, Apache, MySQL/MariaDB, PHP) or LEMP (Linux, Nginx, MySQL/MariaDB, PHP) stack. Tested locally with XAMPP.

## Folder Structure

```
├── css/              # CSS stylesheets
├── js/               # JavaScript files
├── images/           # Image assets
├── php/              # Backend PHP scripts (database connection, API endpoints)
├── database/         # Contains the database schema SQL file (Optional: Create this folder)
├── index.html        # Main landing page
├── login.html        # Login/Registration page
├── survey.html       # Main survey page
├── results.html      # Page to display user results
├── dashboard.html    # Admin dashboard page
├── ... (other HTML files) ...
├── README.md         # This file
├── LICENSE           # Project license file
└── .gitignore        # Specifies intentionally untracked files that Git should ignore
```

## Setup & Installation (Local Development)

1.  **Prerequisites:**

    - A local web server environment like [XAMPP](https://www.apachefriends.org/), MAMP, or WAMP installed (includes Apache, MariaDB/MySQL, PHP).
    - A Git client (like [GitHub Desktop](https://desktop.github.com/) or command-line Git).
    - A web browser.
    - A text editor (e.g., VS Code, Sublime Text).

2.  **Clone Repository:**

    - Clone this repository into your web server's document root directory (e.g., `htdocs` for XAMPP).

    ```bash
    git clone [URL of your GitHub repository] open-digi-survey-website
    ```

    - Or use GitHub Desktop to clone the repository.

3.  **Database Setup:**

    - Start the Apache and MySQL modules in your XAMPP control panel (or equivalent).
    - Open phpMyAdmin (usually accessible via `http://localhost/phpmyadmin`).
    - Create a new database named `open_digi_db` (ensure the collation is set to `utf8mb4_general_ci` or similar).
    - Select the `open_digi_db` database.
    - Go to the "Import" tab.
    - Click "Choose File" and select the `open_digi_db.sql` (or `.txt`) file provided in the `database/` directory of this repository. ( **Action:** You need to export your current database structure to an SQL file and place it here).
    - Click "Go" or "Import" to create the tables.
    - **Important:** Ensure the `dashboard_users` table has at least one user for accessing the dashboard (e.g., user_id 'opendigi', password 'opendigi123' - **change this password in a real deployment!**).

4.  **Configuration:**

    - Navigate to the `php/` directory within your project folder.
    - Open `db_connect.php` in your text editor.
    - Verify the database credentials (`$host`, `$username`, `$password`, `$database`). For default XAMPP, username is usually `root` and password is `""` (empty).

    ```php
    $host = "localhost";
    $username = "root"; // Default XAMPP username
    $password = "";     // Default XAMPP password (usually empty)
    $database = "open_digi_db";
    ```

5.  **Running the Site:**
    - Ensure your Apache server is running.
    - Open your web browser and navigate to `http://localhost/open-digi-survey-website/` (replace `open-digi-survey-website` with your actual project folder name if different).

## Deployment (University Server - open-digi.uni-rostock.de)

1.  **Server Access:** Connect to the server `open-digi.uni-rostock.de` via SSH using the provided credentials (`loginusr` / `1st.Login`). **Change the password immediately** using the `passwd` command. Consider setting up SSH key authentication for better security.
2.  **Install Software Stack:** Install Apache2 or Nginx, PHP (e.g., 8.0+) with required extensions (`mysql`, `mbstring`, `xml`, `json`), and MariaDB/MySQL server using `apt`.
    ```bash
    sudo apt update
    sudo apt install apache2 php libapache2-mod-php php-mysql php-mbstring php-xml php-json mariadb-server # Example for Apache
    # OR for Nginx: sudo apt install nginx php-fpm php-mysql ...
    ```
3.  **Secure Database:** Run `sudo mysql_secure_installation` and follow the prompts. Create the `open_digi_db` database and a dedicated database user with a strong password. Grant privileges to this user for the `open_digi_db` database.
4.  **Deploy Code:** Upload the project files (e.g., using `scp`, FileZilla with SFTP, or `git clone`) to the web server's document root (typically `/var/www/html/` or a subdirectory like `/var/www/html/open-digi-survey-website`).
5.  **Import Database:** Import the `open_digi_db.sql` schema into the MariaDB/MySQL database on the server using the command line or a tool like phpMyAdmin if installed.
6.  **Configure Database Connection:** **Crucially**, edit the `php/db_connect.php` file _on the server_ with the database name, username, and password you created in step 3.
7.  **Configure Web Server:** Create/edit the Apache Virtual Host file (e.g., in `/etc/apache2/sites-available/`) or Nginx server block file to point the domain `open-digi.uni-rostock.de` to the project's root directory (e.g., `/var/www/html/open-digi-survey-website`). Enable the site (`sudo a2ensite ...` for Apache) and restart the web server (`sudo systemctl restart apache2` or `sudo systemctl restart nginx`).
8.  **Configure Firewall (UFW):** Allow HTTP and HTTPS traffic from the world:
    ```bash
    sudo ufw allow 80/tcp comment "http Weltweit"
    sudo ufw allow 443/tcp comment "https Weltweit"
    # Or use profiles: sudo ufw allow 'Apache Full' or sudo ufw allow 'Nginx Full'
    sudo ufw enable # If not already enabled
    ```
    Also ensure SSH access is appropriately restricted as recommended in the setup email.
9.  **File Permissions:** Ensure the web server user (e.g., `www-data`) has read access to all project files and potentially write access to session or log directories if needed.
    ```bash
    sudo chown -R www-data:www-data /var/www/html/open-digi-survey-website # Example ownership change
    sudo find /var/www/html/open-digi-survey-website -type d -exec chmod 755 {} \; # Example directory permissions
    sudo find /var/www/html/open-digi-survey-website -type f -exec chmod 644 {} \; # Example file permissions
    ```

## Key Files Overview

- `js/survey.min.js`: Core logic for survey progression, validation, results display.
- `js/script.min.js`: Handles login, code generation, general UI interactions.
- `js/dashboard.js`: Logic for the administrative dashboard.
- `php/save_data.php`: Endpoint for saving survey answers.
- `php/get_user_data.php`: Endpoint for retrieving user data for results/dashboard.
- `php/login.php`: Handles user login via code.
- `php/register.php`: Handles new user code registration.
- `php/db_connect.php`: Database connection configuration.

## Dependencies (CDN)

- **Chart.js:** Used for rendering competency charts.
- **SweetAlert2:** Used for user-friendly alerts and notifications.
- **Font Awesome:** Used for icons.
- **Google Fonts (Roboto):** Used for typography.
- **html2canvas & jsPDF:** Used for exporting results to PDF.
- **canvas-confetti:** Used for celebratory visual effects.

## Contact

For questions regarding this project, please contact:

- Kenan Trivedi - <NAME_EMAIL>
- Jun.-Prof.in Dr.in Charlott Rubach (Project Lead) - <EMAIL>
- Anne-Kathrin Hirsch (Project Staff) - <EMAIL>
